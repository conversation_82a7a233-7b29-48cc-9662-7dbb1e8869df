@charset "utf-8";
/* CSS Document */

@CHARSET "UTF-8";


#drawTable .page_info {
	width: 691px;
}

#drawInfo .LH_L {
	color: #2836F4;
}

#resultPanel {
	margin-bottom: 15px;
}

#resultPanel table {
	width: 755px;
}

.split_panel .amount{
	width: 65px;
}

.split_panel .name {
  width: 25px;
}

.split_panel {
	float: none;
	overflow: hidden;
}

.split_panel table {
	float: left;
	width: 150px;
	margin: 10px 1px 0 0;
}

.table_ball .ballname {
	width: 35px;
	height: 35px;
}

.table_ball .ballodds {
	width: 79px;
}

.table_gy .name {
	font-weight: bold;
}

.lottery_info .name,#header .result {
	height: 24px;
	line-height: 24px;
}

#drawInfo .draw_number{
	height: auto;
  	line-height: inherit;
  	margin-right: 3px;
}

.table_ball .name span,#drawInfo .balls b {
	background: url("img/ball/ball-pk.png") no-repeat;
	width: 30px;
	height: 30px;
	margin: 0 auto;
	font-size: 0px;
	text-indent: -99999px;
	display: inline-block;
}

#drawInfo .balls b {
	/*background: url("img/ball/ball-pk.png") no-repeat;*/
	width: 30px;
	height: 30px;
	margin: 0 1px;
	margin-top: 6px;
}

.table_ball .name .b1,#drawInfo .balls .b1 {
	background: url("./img/ball/ball/1.png") no-repeat; background-size:30px;
}

.table_ball .name .b2,#drawInfo .balls .b2 {
	background: url("./img/ball/ball/2.png") no-repeat; background-size:30px;
}

.table_ball .name .b3,#drawInfo .balls .b3 {
	background: url("./img/ball/ball/3.png") no-repeat; background-size:30px;
}

.table_ball .name .b4,#drawInfo .balls .b4 {
	background: url("./img/ball/ball/4.png") no-repeat; background-size:30px;
}

.table_ball .name .b5,#drawInfo .balls .b5 {
	background: url("./img/ball/ball/5.png") no-repeat; background-size:30px;
}

.table_ball .name .b6,#drawInfo .balls .b6 {
	background: url("./img/ball/ball/6.png") no-repeat; background-size:30px;
}

.table_ball .name .b7,#drawInfo .balls .b7 {
	background: url("./img/ball/ball/7.png") no-repeat; background-size:30px;
}

.table_ball .name .b8,#drawInfo .balls .b8 {
	background: url("./img/ball/ball/8.png") no-repeat; background-size:30px;
}

.table_ball .name .b9,#drawInfo .balls .b9 {
	background: url("./img/ball/ball/9.png") no-repeat; background-size:30px;
}

.table_ball .name .b10,#drawInfo .balls .b10 {
	background: url("./img/ball/ball/10.png") no-repeat; background-size:30px;
}

