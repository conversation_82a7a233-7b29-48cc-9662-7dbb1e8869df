s.min=a;this.max=c;f=this.max-this.min;if(this.numberTicks==null)if(this.daTickInterval!=null){c=(new e.jsDate(this.max)).diff(this.min,this.daTickInterval[1],true);this.numberTicks=Math.ceil(c/this.daTickInterval[0])+1;this.max=(new e.jsDate(this.min)).add((this.numberTicks-1)*this.daTickInterval[0],this.daTickInterval[1]).getTime()}else this.numberTicks=g>200?parseInt(3+(g-200)/100,10):2;l=f/(this.numberTicks-1)/1E3;if(this.daTickInterval==null)this.daTickInterval=[l,"seconds"];for(c=0;c<
this.numberTicks;c++){a=new e.jsDate(this.min);d=a.add(c*this.daTickInterval[0],this.daTickInterval[1]).getTime();a=new this.tickRenderer(this.tickOptions);if(this.showTicks){if(!this.showTickMarks)a.showMark=false}else{a.showLabel=false;a.showMark=false}a.setTick(d,this.name);this._ticks.push(a)}}if(this.tickInset){this.min-=this.tickInset*l;this.max+=this.tickInset*l}if(this._daTickInterval==null)this._daTickInterval=this.daTickInterval}})(jQuery);
                                                                         ,21],[-1,-1],[12,4],[18,-2]]},R:{width:21,points:[[4,21],[4,0],[-1,-1],[4,21],[13,21],[16,20],[17,19],[18,17],[18,15],[17,13],[16,12],[13,11],[4,11],[-1,-1],[11,11],[18,0]]},S:{width:20,points:[[17,18],[15,20],[12,21],[8,21],[5,20],[3,18],[3,16],[4,14],[5,13],[7,12],[13,10],[15,9],[16,8],[17,6],[17,3],[15,1],[12,0],[8,0],[5,1],[3,3]]},T:{width:16,points:[[8,21],[8,0],[-1,-1],[1,21],[15,21]]},U:{width:22,points:[[4,21],[4,6],[5,3],[7,1],[10,0],[12,0],[15,
1],[17,3],[18,6],[18,21]]},V:{width:18,points:[[1,21],[9,0],[-1,-1],[17,21],[9,0]]},W:{width:24,points:[[2,21],[7,0],[-1,-1],[12,21],[7,0],[-1,-1],[12,21],[17,0],[-1,-1],[22,21],[17,0]]},X:{width:20,points:[[3,21],[17,0],[-1,-1],[17,21],[3,0]]},Y:{width:18,points:[[1,21],[9,11],[9,0],[-1,