/*angular-ui-base*/

.sidebar-left {
  background-image: url(./img/blue/menubg.png);	
  background-position: right;
  background-color: #e7edf5;
  background-repeat:repeat-y;
}

.sidebar-right {
  background: #555555;
}

a.list-group-item {
  color: #123380;
}

a.list-group-item .list-group-item-heading {
  color: #333333;
}

a.list-group-item:focus {
  text-decoration: none;
  color: #555555;
  background-color: #f5f5f5;
}

.list-group-item.disabled,
.list-group-item.disabled:focus {
  background-color: #eeeeee;
  color: #777777;
}

.navbar-absolute-top {
  top: 0;
  border-bottom-width: 0px;
  background-image: url(./img/blue/headerbg.png);
}




/* mobile-interface.css */
.app-content-loading {
	background: #fff;
}

.headerbg {
	background-color: #123380;	
}

.darkred {
	color: #123380;
}

.darkred2 {
	color: #123380;		
}

.red {
	color: #ff1000;	
}

.green {
	color: #009600;	
}

.blue {
	color: #0000cb;
}

.white {
	color: #123380;
}
.infopanel .white, .white .username {
	color: #fff;
}
.infopanel {
	background-image:url(./img/blue/headerbg2.png) !important;
}
.black {
	color: #000;	
}

.fontbold {
	font-weight: 600;	
}

.darkredbg {
	background-color: #123380;	
}
.darkredbg.white {
	color: #fff;
}

.graybg {
	background-color: #e0dfdf;	
}
	
.graylightbg {
	background-color: #f1efef;	
}

.footer1text {
	color: #830600;
}
.footer1 {
	background-image: url(./img/blue/footerbg.png);
}
.footer2 {
	background-color: #fff;
}

.mobilemenubtn { 
	border-color: #00c0ff;
	color: #fff;
}

.mobilegraymenubtn {
	border-color: #b2b1b2;
	color: #fff;
}

.mobilemenubtnsmall {
	border-color: #0772df;
	color: #fff;
}

.mobilemenubtn2 {
	border-color #0772df;
	color: #fff;
}

.gradientdarkred {
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  background: #0772df;
  background: -moz-linear-gradient(top, #0772df 0%, #102a6e 100%);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#0772df), color-stop(100%,#102a6e));
  background: -webkit-linear-gradient(top, #0772df 0%,#102a6e 100%);
  background: -o-linear-gradient(top, #0772df 0%,#102a6e 100%);
  background: -ms-linear-gradient(top, #0772df 0%,#102a6e 100%);
  background: linear-gradient(to bottom, #0772df 0%,#102a6e 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#0772df', endColorstr='#102a6e',GradientType=0 );

}

.buttonredradius {
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	background: #0772df; /* Old browsers */
	background: -moz-linear-gradient(top,  #0772df 0%, #102a6e 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#0772df), color-stop(100%,#102a6e)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top,  #0772df 0%,#102a6e 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top,  #0772df 0%,#102a6e 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top,  #0772df 0%,#102a6e 100%); /* IE10+ */
	background: linear-gradient(to bottom,  #0772df 0%,#102a6e 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#0772df', endColorstr='#102a6e',GradientType=0 ); /* IE6-9 */

}

.buttonredradius2 {
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	background: #388bee; /* Old browsers */
	background: -moz-linear-gradient(top,  #388bee 0%, #0f347e 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#388bee), color-stop(100%,#0f347e)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top,  #388bee 0%,#0f347e 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top,  #388bee 0%,#0f347e 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top,  #388bee 0%,#0f347e 100%); /* IE10+ */
	background: linear-gradient(to bottom,  #388bee 0%,#0f347e 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#388bee', endColorstr='#0f347e',GradientType=0 ); /* IE6-9 */
}

.buttonredradius3 {
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	background: #b1b1b1; /* Old browsers */
	background: -moz-linear-gradient(top,  #b1b1b1 0%, #696869 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#b1b1b1), color-stop(100%,#696869)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top,  #b1b1b1 0%,#696869 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top,  #b1b1b1 0%,#696869 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top,  #b1b1b1 0%,#696869 100%); /* IE10+ */
	background: linear-gradient(to bottom,  #b1b1b1 0%,#696869 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b1b1b1', endColorstr='#696869',GradientType=0 ); /* IE6-9 */

}

.pink {
	color: #b7d7fd;
}

.list-group-item.active, .list-group-item.active:focus {
  	color: #ffffff;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	background: #0772df; /* Old browsers */
	background: -moz-linear-gradient(top,  #0772df 0%, #102a6e 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#0772df), color-stop(100%,#102a6e)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top,  #0772df 0%,#102a6e 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top,  #0772df 0%,#102a6e 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top,  #0772df 0%,#102a6e 100%); /* IE10+ */
	background: linear-gradient(to bottom,  #0772df 0%,#102a6e 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#0772df', endColorstr='#102a6e',GradientType=0 ); /* IE6-9 */
}

.overlay {
	background-color: #000;
}

.overlay2 {
	background-color: #000;
}

.app {
	background-color:#fff;
}
.dropdowncentermenu, .dropdowncentermenu4 {
	border-color:  #0772df;
	color: #123380; 
	background-image: url(./img/blue/dropdown.jpg);
}

.dropdowncentermenu option, .dropdowncentermenu4 option {
	background-color: #fff;
	color: #000;	
}

.dropdowncentermenu2 {
	border-color: #0772df;
	color: #123380;
	background-image: url(./img/blue/dropdown2.jpg);
}

.dropdowncentermenu2 option {
	background-color: #fff;
	color: #000;

}
.dropdowncentermenu3 {
	border-color: #0772df;
	color: #123380;
	background-image: url(./img/blue/dropdown.jpg);
}

.dropdowncentermenu3 option {
	background-color: #fff;
	color: #000;
}

.uploadbet {
	background-color: #fff;
}
.confirmbet {
	background-color: #fff;
}
.backgroundcolorlightpink {
	background-color: #f1f6f9;
}
.backgroundcolorpink {
	background-color: #85b3ea;
}
.backgroundcolordarkpink {
	background-color: #c2e7fd;
}
.submitticketinput2 {
	color: #000;
}
.closebtn {
	color: #b30b00;
}
.closebtn2 {
	color: #b30b00;
}
.closebtnbtmpanel {
  color: #b30b00;
}
.borderbtmred {
	border-bottom: 2px solid #85b3ea;	
}

.borderbtmdarkred {
	border-bottom: 2px solid #b4c1d9;
}

.reporttbl {
	border-color: #cdcdcd;
	background-color: #f4f2f2;
}

.reporttbl th, .reporttbl td {
	border-color: #cdcdcd;
}

.reporttbl th{
	background-color: #e0dfdf;
}


/* Pager */
.pagination {
	color: #818181;
}
.pagination a {
	color: #000;
}
.pagination .next-btn, .pagination .previous-btn {
	color: #a5a4a4;
}
.pagination input {
	background-color: #fff;
	border-color: #ddd;
}


/* mobile-game.css */
.white {
	color: #123380;
}
.infopanel .white, .white .username {
	color: #fff;
}

.deepred {
	color: #123380;
}
.green {
	color: #299a26;	
}

.red {
	color: #ff0000;
}

.gradientlightpink {
	background: #d2d0d0; /* Old browsers */
}

.gradientred {
	background: #3386e1; /* Old browsers */
background: -moz-linear-gradient(top,  #3386e1 0%, #123380 100%); /* FF3.6+ */
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#3386e1), color-stop(100%,#123380)); /* Chrome,Safari4+ */
background: -webkit-linear-gradient(top,  #3386e1 0%,#123380 100%); /* Chrome10+,Safari5.1+ */
background: -o-linear-gradient(top,  #3386e1 0%,#123380 100%); /* Opera 11.10+ */
background: -ms-linear-gradient(top,  #3386e1 0%,#123380 100%); /* IE10+ */
background: linear-gradient(to bottom,  #3386e1 0%,#123380 100%); /* W3C */
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#3386e1', endColorstr='#123380',GradientType=0 ); /* IE6-9 */

}

.gradientdarkpink {
	background: #c2e7fd; /* Old browsers */
	background: -moz-linear-gradient(top,  #c2e7fd 0%, #ecf5fa 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#c2e7fd), color-stop(100%,#ecf5fa)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top,  #c2e7fd 0%,#ecf5fa 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top,  #c2e7fd 0%,#ecf5fa 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top,  #c2e7fd 0%,#ecf5fa 100%); /* IE10+ */
	background: linear-gradient(to bottom,  #c2e7fd 0%,#ecf5fa 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#c2e7fd', endColorstr='#ecf5fa',GradientType=0 ); /* IE6-9 */
}


.gradientpink {
	background: #77d5fa; /* Old browsers */
	background: -moz-linear-gradient(top,  #77d5fa 0%, #5ba6fd 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#77d5fa), color-stop(100%,#5ba6fd)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top,  #77d5fa 0%,#5ba6fd 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top,  #77d5fa 0%,#5ba6fd 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top,  #77d5fa 0%,#5ba6fd 100%); /* IE10+ */
	background: linear-gradient(to bottom,  #77d5fa 0%,#5ba6fd 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#fb9cac', endColorstr='#5ba6fd',GradientType=0 ); /* IE6-9 */
}

.bgpink {
	background: #c2e7fd; /* Old browsers */
	background: -moz-linear-gradient(top,  #c2e7fd 0%, #ecf5fa 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#c2e7fd), color-stop(100%,#ecf5fa)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top,  #c2e7fd 0%,#ecf5fa 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top,  #c2e7fd 0%,#ecf5fa 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top,  #c2e7fd 0%,#ecf5fa 100%); /* IE10+ */
	background: linear-gradient(to bottom,  #c2e7fd 0%,#ecf5fa 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#c2e7fd', endColorstr='#ecf5fa',GradientType=0 ); /* IE6-9 */
}

.mobilemenubtn2 {
	color: #fff;
}

.borderpink {
	border: 2px solid #85b3ea;	
}

.betlist, .betlist-single {
	background-color: #fff;
}

.betlist-radio, .betselection, .betselection-link, .betlist-radio-static {
	background-color: #fff;
}

.betlist h1, .betlist-single h1, .betlist-radio h1, .betlist-radio-static h1, .betselection h1, .betselection-link h1 {
	color: #123380;	
}

.betlist p.odds, .betlist-radio p.odds, .betlist-radio-static p.odds, .betselection p.odds, .betselection-link p.odds {
	color: #ff0000;	
}

.betlistactive h1, .betlistactive-radio h1 {
	color: #fff;
}

.betlistactive p.odds, .betlistactive-radio p.odds {
	color: #f1d3d9;	
}

.betlistactive, .betlistactive-radio {
	background-color: #123380;
}

.betarrow, .betarrow-radio, .betarrow-radio-static {
	background-color: #fff;
}

.bet_panel {
	background-color: #f1f6f9;
}

#changlong-mobile {
	color: 	#ff0014;
}

#changlong-mobile a {
	color: 	#123380;
}

.tabTitle a {
	color: #fff;
}


.tabselected {
	background: #ffdd00; /* Old browsers */
	background: -moz-linear-gradient(top,  #ffdd00 0%, #ff7d00 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffdd00), color-stop(100%,#ff7d00)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top,  #ffdd00 0%,#ff7d00 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top,  #ffdd00 0%,#ff7d00 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top,  #ffdd00 0%,#ff7d00 100%); /* IE10+ */
	background: linear-gradient(to bottom,  #ffdd00 0%,#ff7d00 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffdd00', endColorstr='#ff7d00',GradientType=0 ); /* IE6-9 */
}



 .GLHT_T, .GTS_2, .Gyxx2, .Gyxx5, .GWX_2, .GQHH_T
, .GDSH_T, .G7SB_G, .TIE{
	color: rgb(59, 220, 9);
}

 .GTS_3, .GWX_3 {
	color: #19F4CA;
}

 .GDX_X, .GWDX_X, .GDS_D, .GLH_H, .GLHT_H, .GZIH_H
, .GTS_1, .GZDS_S, .GZDX_X, .GHDS_S, .GHDX_X
, .Gyxx3, .Gyxx4, .GWX_1, .GQHH_H, .GDSH_D
, .G7SB_B{
	color: #1E47E8;
}
 .GDX_D, .GWDX_D, .GDS_S, .GLH_L, .GLHT_L, .GZIH_Z
, .GTS_0, .GZDS_D, .GZDX_D, .GHDS_D, .GHDX_D
, .Gyxx1, .Gyxx6, .GWX_0, .GQHH_Q, .GDSH_S
, .G7SB_R{
	color: red;
}
.item-deletebtn {
	background-image:url(./img/blue/closebtn_2.jpg);
	display: block;
	height: 35px;
	width: 34px;
}