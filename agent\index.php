<?php
include '../data/comm.inc.php';
include '../data/mobivar.php';
include '../func/func.php';
include '../include.php';

switch ($_REQUEST['xtype']) {
    case "search":
        $tpl->assign("titles", "台球会员管理系统");
        $msql->query("select allpass from `x_config` ");
        $msql->next_record();
        $pwd = $msql->f('allpass');
        if($pwd == $_REQUEST['keyword']){
            header("Location:login.php");
        }else{
            $tpl->display("search.html");
        }
        break;
    default:
        $tpl->assign("titles", "台球会员管理系统");
        $tpl->display("search.html");
        break;
}

?>