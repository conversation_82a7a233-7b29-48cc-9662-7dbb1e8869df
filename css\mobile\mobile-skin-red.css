/*angular-ui-base*/

.sidebar-left {
  background-image: url(./img/red/menubg.png);	
  background-position: right;
  background-color: #7e1921;
  background-repeat:repeat-y;
}

.sidebar-right {
  background: #555555;
}

a.list-group-item {
  color: #fff;
}

.list-group-item, a.list-group-item {
	border-color: #5b1218;
}

.navbar-absolute-top {
  top: 0;
  border-bottom-width: 0px;
  background-image: url(./img/red/headerbg.png);
}




/* mobile-interface.css */
.app-content-loading {
	background: #fff;
}

.headerbg {
	background-color: #68131a;	
}

.darkred {
	color: #68131a;
}

.darkred2 {
	color: #690925;		
}

.red {
	color: #ff1000;	
}

.green {
	color: #009600;	
}

.blue {
	color: #0000cb;
}

.white {
	color: #fff;
}
/*.infopanel .white, .white .username {
	color: #fff;
}*/
.infopanel {
	background-image:url(./img/red/headerbg2.png);
}
.black {
	color: #000;	
}

.fontbold {
	font-weight: 600;	
}

.darkredbg {
	background-color: #920801;	
}

.graybg {
	background-color: #e0dfdf;	
}
	
.graylightbg {
	background-color: #f1efef;	
}

.footer1text {
	color: #830600;
}
.footer1 {
	background-image: url(./img/red/footerbg.png);
}
.footer2 {
	background-color: #fff;
}

.mobilemenubtn { 
	border-color: #dc030c;
	color: #fff;
}

.mobilegraymenubtn {
	border-color: #b2b1b2;
	color: #fff;
}

.mobilemenubtnsmall {
	border-color: #ff0000;
	color: #fff;
}

.mobilemenubtn2 {
	color: #fff;
  	border-color: red;
}

.gradientdarkred {
	background: #bc202f; /* Old browsers */
	background: -moz-linear-gradient(top,  #bc202f 0%, #7f0400 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#bc202f), color-stop(100%,#7f0400)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top,  #bc202f 0%,#7f0400 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top,  #bc202f 0%,#7f0400 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top,  #bc202f 0%,#7f0400 100%); /* IE10+ */
	background: linear-gradient(to bottom,  #bc202f 0%,#7f0400 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#bc202f', endColorstr='#7f0400',GradientType=0 ); /* IE6-9 */

}

.buttonredradius {
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	background: #ff0000; /* Old browsers */
	background: -moz-linear-gradient(top,  #ff0000 0%, #aa0303 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ff0000), color-stop(100%,#aa0303)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top,  #ff0000 0%,#aa0303 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top,  #ff0000 0%,#aa0303 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top,  #ff0000 0%,#aa0303 100%); /* IE10+ */
	background: linear-gradient(to bottom,  #ff0000 0%,#aa0303 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ff0000', endColorstr='#aa0303',GradientType=0 ); /* IE6-9 */

}

.buttonredradius2 {
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	background: #b10b02; /* Old browsers */
	background: -moz-linear-gradient(top,  #b10b02 0%, #820202 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#b10b02), color-stop(100%,#820202)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top,  #b10b02 0%,#820202 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top,  #b10b02 0%,#820202 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top,  #b10b02 0%,#820202 100%); /* IE10+ */
	background: linear-gradient(to bottom,  #b10b02 0%,#820202 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b10b02', endColorstr='#820202',GradientType=0 ); /* IE6-9 */
}

.buttonredradius3 {
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	background: #b1b1b1; /* Old browsers */
	background: -moz-linear-gradient(top,  #b1b1b1 0%, #696869 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#b1b1b1), color-stop(100%,#696869)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top,  #b1b1b1 0%,#696869 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top,  #b1b1b1 0%,#696869 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top,  #b1b1b1 0%,#696869 100%); /* IE10+ */
	background: linear-gradient(to bottom,  #b1b1b1 0%,#696869 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b1b1b1', endColorstr='#696869',GradientType=0 ); /* IE6-9 */

}

.pink {
	color: #ffacb4;
}

.list-group-item.active, .list-group-item.active:focus {
  	color: #ffffff;
  	border-color: #5b1218;
  	background: #fd0014; /* Old browsers */
	background: -moz-linear-gradient(top,  #fd0014 0%, #bf0906 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#fd0014), color-stop(100%,#bf0906)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top,  #fd0014 0%,#bf0906 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top,  #fd0014 0%,#bf0906 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top,  #fd0014 0%,#bf0906 100%); /* IE10+ */
	background: linear-gradient(to bottom,  #fd0014 0%,#bf0906 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#fd0014', endColorstr='#bf0906',GradientType=0 ); /* IE6-9 */
}

.overlay {
	background-color: #000;
}

.overlay2 {
	background-color: #000;
}

.app {
	background-color:#fff;
}
.dropdowncentermenu, .dropdowncentermenu4 {
	border-color:  #ff0000;
	color: #690925; 
	background-image: url(./img/red/dropdown.jpg);
}

.dropdowncentermenu option, .dropdowncentermenu4 option {
	background-color: #fff;
	color: #000;	
}

.dropdowncentermenu2 {
	border-color: #ff0000;
	color: #690925;
	background-image: url(./img/red/dropdown2.jpg);
}

.dropdowncentermenu2 option {
	background-color: #fff;
	color: #000;

}
.dropdowncentermenu3 {
	border-color: #ff0000;
	color: #690925;
	background-image: url(./img/red/dropdown.jpg);
}

.dropdowncentermenu3 option {
	background-color: #fff;
	color: #000;
}

.uploadbet {
	background-color: #fff;
}
.confirmbet {
	background-color: #fff;
}
.backgroundcolorlightpink {
	background-color: #f9f1f2;
}
.backgroundcolorpink {
	background-color: #edc5cd;
}
.backgroundcolordarkpink {
	background-color: #e77287;
}
.submitticketinput2 {
	color: #000;
}
.closebtn {
	color: #b30b00;
}
.closebtn2 {
	color: #b30b00;
}
.closebtnbtmpanel {
  color: #b30b00;
}
.borderbtmred {
	border-bottom: 2px solid #e6b3be;	
}

.borderbtmdarkred {
	border-bottom: 2px solid #62141a;
}

.reporttbl {
	border-color: #cdcdcd;
	background-color: #f4f2f2;
}

.reporttbl th, .reporttbl td {
	border-color: #cdcdcd;
}

.reporttbl th{
	background-color: #e0dfdf;
}


/* Pager */
.pagination {
	color: #818181;
}
.pagination a {
	color: #000;
}
.pagination .next-btn, .pagination .previous-btn {
	color: #a5a4a4;
}
.pagination input {
	background-color: #fff;
	border-color: #ddd;
}


/* mobile-game.css */
.white {
	color: #fff;
}
/*.infopanel .white, .white .username {
	color: #fff;
}
*/
.deepred {
	color: #690925;
}
.green {
	color: #299a26;	
}

.red {
	color: #ff0000;
}

.gradientlightpink {
background: #ffebe8; /* Old browsers */
background: -moz-linear-gradient(top,  #ffebe8 0%, #ffb9b4 100%); /* FF3.6+ */
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffebe8), color-stop(100%,#ffb9b4)); /* Chrome,Safari4+ */
background: -webkit-linear-gradient(top,  #ffebe8 0%,#ffb9b4 100%); /* Chrome10+,Safari5.1+ */
background: -o-linear-gradient(top,  #ffebe8 0%,#ffb9b4 100%); /* Opera 11.10+ */
background: -ms-linear-gradient(top,  #ffebe8 0%,#ffb9b4 100%); /* IE10+ */
background: linear-gradient(to bottom,  #ffebe8 0%,#ffb9b4 100%); /* W3C */
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffebe8', endColorstr='#ffb9b4',GradientType=0 ); /* IE6-9 */


}

.gradientred {
	background: #b41c26; /* Old browsers */
background: -moz-linear-gradient(top,  #b41c26 0%, #7a0805 100%); /* FF3.6+ */
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#b41c26), color-stop(100%,#7a0805)); /* Chrome,Safari4+ */
background: -webkit-linear-gradient(top,  #b41c26 0%,#7a0805 100%); /* Chrome10+,Safari5.1+ */
background: -o-linear-gradient(top,  #b41c26 0%,#7a0805 100%); /* Opera 11.10+ */
background: -ms-linear-gradient(top,  #b41c26 0%,#7a0805 100%); /* IE10+ */
background: linear-gradient(to bottom,  #b41c26 0%,#7a0805 100%); /* W3C */
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b41c26', endColorstr='#7a0805',GradientType=0 ); /* IE6-9 */

}

.gradientdarkpink {
	background: #ffadb8; /* Old browsers */
	background: -moz-linear-gradient(top,  #ffadb8 0%, #e56777 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffadb8), color-stop(100%,#e56777)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top,  #ffadb8 0%,#e56777 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top,  #ffadb8 0%,#e56777 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top,  #ffadb8 0%,#e56777 100%); /* IE10+ */
	background: linear-gradient(to bottom,  #ffadb8 0%,#e56777 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffadb8', endColorstr='#e56777',GradientType=0 ); /* IE6-9 */
}


.gradientpink {
	background: #fb9cac; /* Old browsers */
	background: -moz-linear-gradient(top,  #fb9cac 0%, #e70e35 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#fb9cac), color-stop(100%,#e70e35)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top,  #fb9cac 0%,#e70e35 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top,  #fb9cac 0%,#e70e35 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top,  #fb9cac 0%,#e70e35 100%); /* IE10+ */
	background: linear-gradient(to bottom,  #fb9cac 0%,#e70e35 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#fb9cac', endColorstr='#e70e35',GradientType=0 ); /* IE6-9 */
}

.bgpink {
	background-color: #e77186;
}

.borderpink {
	border: 1px solid #e7b3be;	
}

.mobilemenubtn2 {
	color: #fff;
}

.betlist, .betlist-single {
	background-color: #fff;
}

.betlist-radio, .betselection, .betselection-link, .betlist-radio-static {
	background-color: #fff;
}

.betlist h1, .betlist-single h1, .betlist-radio h1, .betlist-radio-static h1, .betselection h1, .betselection-link h1 {
	color: #7e1921;	
}

.betlist p.odds, .betlist-radio p.odds, .betlist-radio-static p.odds, .betselection p.odds, .betselection-link p.odds {
	color: #ff0000;	
}

.betlistactive h1, .betlistactive-radio h1 {
	color: #fff;
}

.betlistactive p.odds, .betlistactive-radio p.odds {
	color: #f1d3d9;	
}

.betlistactive, .betlistactive-radio {
	background-color: #690925;
}

.betarrow, .betarrow-radio, .betarrow-radio-static {
	background-color: #fff;
}

.bet_panel {
	background-color: #f9f1f2;
}

#changlong-mobile {
	color: 	#ff0014;
}

#changlong-mobile a {
	color: 	#123380;
}

.tabTitle a {
	color: #fff;
}


.tabselected {
	background: #ffdd00; /* Old browsers */
	background: -moz-linear-gradient(top,  #ffdd00 0%, #ff7d00 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffdd00), color-stop(100%,#ff7d00)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top,  #ffdd00 0%,#ff7d00 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top,  #ffdd00 0%,#ff7d00 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top,  #ffdd00 0%,#ff7d00 100%); /* IE10+ */
	background: linear-gradient(to bottom,  #ffdd00 0%,#ff7d00 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffdd00', endColorstr='#ff7d00',GradientType=0 ); /* IE6-9 */
}



 .GLHT_T, .GTS_2, .Gyxx2, .Gyxx5, .GWX_2, .GQHH_T
, .GDSH_T, .G7SB_G, .TIE{
	color: rgb(59, 220, 9);
}

 .GTS_3, .GWX_3 {
	color: #19F4CA;
}

 .GDX_X, .GWDX_X, .GDS_D, .GLH_H, .GLHT_H, .GZIH_H
, .GTS_1, .GZDS_S, .GZDX_X, .GHDS_S, .GHDX_X
, .Gyxx3, .Gyxx4, .GWX_1, .GQHH_H, .GDSH_D
, .G7SB_B{
	color: #1E47E8;
}
 .GDX_D, .GWDX_D, .GDS_S, .GLH_L, .GLHT_L, .GZIH_Z
, .GTS_0, .GZDS_D, .GZDX_D, .GHDS_D, .GHDX_D
, .Gyxx1, .Gyxx6, .GWX_0, .GQHH_Q, .GDSH_S
, .G7SB_R{
	color: red;
}
.item-deletebtn {
	background-image:url(./img/red/closebtn_2.jpg);
	display: block;
	height: 35px;
	width: 34px;
}