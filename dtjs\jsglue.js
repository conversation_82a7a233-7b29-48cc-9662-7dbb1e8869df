var Flask = new(function () {
  'use strict';
  return {
    '_endpoints': [["master.monitor_gambles", ["/master/monitor/gambles/", "/", "/", ""], ["t", "number", "page"]], ["master.bet_shengxiaolian", ["/master/system/bet/shengxiaolian/", "/", ""], ["t", "page"]], ["master.bet_tepingzhong", ["/master/system/bet/tepingzhong/", "/", ""], ["t", "page"]], ["master.bet_weishulian", ["/master/system/bet/weishulian/", "/", ""], ["t", "page"]], ["master.bet_zhengmate", ["/master/system/bet/zhengmate/", "/", ""], ["t", "page"]], ["master.bet_buzhong", ["/master/system/bet/buzhong/", "/", ""], ["t", "page"]], ["master.bet_zhongyi", ["/master/system/bet/zhongyi/", "/", ""], ["t", "page"]], ["master.bet_lianma", ["/master/system/bet/lianma/", "/", ""], ["t", "page"]], ["master.bet_qita", ["/master/system/bet/qita/", "/", ""], ["t", "page"]], ["agent.daily_detail2", ["/agent/master/daily/detail/", "/", ""], ["member_id", "page"]], ["master.transfers_change_single", ["/master/transfers/change_single/", "/", ""], ["period", "waidiao_id"]], ["master.transfers_change_group", ["/master/transfers/change_group/", "/", ""], ["period", "waidiao_id"]], ["master.transfers_change", ["/master/transfers/change/", "/", ""], ["period", "waidiao_id"]], ["master.monitor_gambles", ["/master/monitor/gambles/", "/", ""], ["t", "number"]], ["master.billing_groups", ["/master/billing/groups/", "/", ""], ["period", "gamble_id"]], ["master.daily_detail", ["/master/daily/detail/", "/", ""], ["member_id", "page"]], ["agent.gambled_groups", ["/agent/gambled/groups/", "/", ""], ["period", "gamble_id"]], ["god.masters_status", ["/god/masters/status/", "/", ""], ["member_id", "status"]], ["member.daily_groups", ["/member/daily_groups/", "/", ""], ["period", "gamble_id"]], ["master.transfers_member", ["/master/transfers/", "/", ""], ["member_id", "page"]], ["agent.daily_general_agent_directly2", ["/agent/master/general_agent/daily/directly/", ""], ["general_agent_id"]], ["agent.daily_shareholder_directly2", ["/agent/master/shareholder/daily/directly/", ""], ["shareholder_id"]], ["agent.daily_company_directly2", ["/agent/master/company/daily/directly/", ""], ["company_id"]], ["agent.daily_branch_directly2", ["/agent/master/branch/daily/directly/", ""], ["branch_id"]], ["master.daily_general_agent_directly", ["/master/general_agent/daily/directly/", ""], ["general_agent_id"]], ["master.daily_shareholder_directly", ["/master/shareholder/daily/directly/", ""], ["shareholder_id"]], ["master.monitor_zhengmate_quick_transfer", ["/master/monitor/zhengmate/quick_transfer/", ""], ["t"]], ["master.monitor_transfer_tepingzhong", ["/master/monitor/transfer/tepingzhong/", ""], ["t"]], ["master.monitor_transfer_buzhong", ["/master/monitor/transfer/buzhong/", ""], ["t"]], ["master.monitor_transfer_zhongyi", ["/master/monitor/transfer/zhongyi/", ""], ["t"]], ["master.monitor_transfer_lianma", ["/master/monitor/transfer/lianma/", ""], ["t"]], ["master.monitor_zhengma_quick_transfer", ["/master/monitor/zhengma/quick_transfer/", ""], ["t"]], ["master.daily_company_directly", ["/master/company/daily/directly/", ""], ["company_id"]], ["master.monitor_tema_quick_transfer", ["/master/monitor/tema/quick_transfer/", ""], ["t"]], ["master.daily_branch_directly", ["/master/branch/daily/directly/", ""], ["branch_id"]], ["master.system_odds_maximum", ["/master/system/odds/maximum/", ""], ["idx"]], ["master.bet_shengxiaolian", ["/master/system/bet/shengxiaolian/", ""], ["t"]], ["master.bet_tepingzhong", ["/master/system/bet/tepingzhong/", ""], ["t"]], ["master.bet_weishulian", ["/master/system/bet/weishulian/", ""], ["t"]], ["master.bet_zhengmate", ["/master/system/bet/zhengmate/", ""], ["t"]], ["master.bet_liangmian", ["/master/system/bet/liangmian/", ""], ["page"]], ["master.bet_zhengma", ["/master/system/bet/zhengma/", ""], ["page"]], ["master.bet_liuxiao", ["/master/system/bet/liuxiao/", ""], ["page"]], ["master.bet_buzhong", ["/master/system/bet/buzhong/", ""], ["t"]], ["master.bet_zhongyi", ["/master/system/bet/zhongyi/", ""], ["t"]], ["master.bet_yixiao", ["/master/system/bet/yixiao/", ""], ["page"]], ["master.bet_weishu", ["/master/system/bet/weishu/", ""], ["page"]], ["master.bet_lianma", ["/master/system/bet/lianma/", ""], ["t"]], ["master.bet_tema", ["/master/system/bet/tema/", ""], ["page"]], ["master.bet_qita", ["/master/system/bet/qita/", ""], ["t"]], ["master.bet_bose", ["/master/system/bet/bose/", ""], ["page"]], ["master.users_general_agent_edit", ["/master/users/general_agent/edit/", ""], ["general_agent_id"]], ["master.users_general_agent_new", ["/master/users/general_agent/new/", ""], ["shareholder_id"]], ["master.users_sub_accounts_delete", ["/master/users/sub_accounts/delete/", ""], ["account_id"]], ["master.users_sub_accounts_edit", ["/master/users/sub_accounts/edit/", ""], ["account_id"]], ["master.users_shareholder_edit", ["/master/users/shareholder/edit/", ""], ["shareholder_id"]], ["master.users_shareholder_new", ["/master/users/shareholder/new/", ""], ["branch_id"]], ["master.users_transfer_edit", ["/master/users/transfer/edit/", ""], ["member_id"]], ["master.users_transfer_new", ["/master/users/transfer/new/", ""], ["agent_id"]], ["master.users_company_edit", ["/master/users/company/edit/", ""], ["company_id"]], ["master.users_branch_edit", ["/master/users/branch/edit/", ""], ["branch_id"]], ["master.users_branch_new", ["/master/users/branch/new/", ""], ["company_id"]], ["master.users_agent_edit", ["/master/users/agent/edit/", ""], ["agent_id"]], ["master.users_agent_new", ["/master/users/agent/new/", ""], ["general_agent_id"]], ["master.users_user_edit", ["/master/users/user/edit/", ""], ["member_id"]], ["master.users_user_new", ["/master/users/user/new/", ""], ["agent_id"]], ["agent.daily_general_agent", ["/agent/master/general_agent/daily/", ""], ["general_agent_id"]], ["agent.shareholder_waidiao2", ["/agent/master/shareholder/waidiao/", ""], ["shareholder_id"]], ["agent.daily_shareholder", ["/agent/master/shareholder/daily/", ""], ["shareholder_id"]], ["agent.company_waidiao2", ["/agent/master/company/waidiao/", ""], ["company_id"]], ["agent.general_waidiao2", ["/agent/master/general/waidiao/", ""], ["general_agent_id"]], ["agent.daily_company2", ["/agent/master/company/daily/", ""], ["company_id"]], ["agent.branch_waidiao2", ["/agent/master/branch/waidiao/", ""], ["branch_id"]], ["agent.daily_branch", ["/agent/master/branch/daily/", ""], ["branch_id"]], ["agent.master_waidiao_detail", ["/agent/master/agent/waidiao/", ""], ["member_id"]], ["agent.agent_waidiao2", ["/agent/master/agent/waidiao/", ""], ["agent_id"]], ["agent.daily_detail2", ["/agent/master/daily/detail/", ""], ["member_id"]], ["agent.daily_agent", ["/agent/master/agent/daily/", ""], ["agent_id"]], ["agent.users_general_agent_delete", ["/agent/users/general_agent/delete/", ""], ["general_agent_id"]], ["agent.users_general_agent_edit", ["/agent/users/general_agent/edit/", ""], ["general_agent_id"]], ["agent.users_shareholder_delete", ["/agent/users/shareholder/delete/", ""], ["shareholder_id"]], ["agent.users_shareholder_edit", ["/agent/users/shareholder/edit/", ""], ["shareholder_id"]], ["agent.users_branch_delete", ["/agent/users/branch/delete/", ""], ["branch_id"]], ["agent.users_branch_edit", ["/agent/users/branch/edit/", ""], ["branch_id"]], ["agent.users_agent_delete", ["/agent/users/agent/delete/", ""], ["agent_id"]], ["agent.users_agent_edit", ["/agent/users/agent/edit/", ""], ["agent_id"]], ["realtime.monitor_tepingzhong", ["/realtime/monitor/monitor_tepingzhong/", ""], ["t"]], ["realtime.monitor_buzhong", ["/realtime/monitor/monitor_buzhong/", ""], ["t"]], ["realtime.monitor_zhongyi", ["/realtime/monitor/monitor_zhongyi/", ""], ["t"]], ["realtime.monitor_lianma", ["/realtime/monitor/monitor_lianma/", ""], ["t"]], ["realtime.monitor_zhengmate", ["/realtime/monitor/zhengmate/", ""], ["t"]], ["realtime.monitor_zhengma", ["/realtime/monitor/zhengma/", ""], ["t"]], ["realtime.monitor_tema", ["/realtime/monitor/tema/", ""], ["t"]], ["realtime.lianma_shengxiaoduipeng", ["/realtime/lianma/shengxiaoduipeng/", ""], ["t"]], ["realtime.lianma_shengweiduipeng", ["/realtime/lianma/shengweiduipeng/", ""], ["t"]], ["realtime.lianma_weishuduipeng", ["/realtime/lianma/weishuduipeng/", ""], ["t"]], ["realtime.lianma_renyiduipeng", ["/realtime/lianma/renyiduipeng/", ""], ["t"]], ["realtime.lianma_tuotou", ["/realtime/lianma/tuotou/", ""], ["t"]], ["realtime.lianma_fushi", ["/realtime/lianma/fushi/", ""], ["t"]], ["member.duoxuanbuzhong_fushi", ["/member/duoxuanbuzhong/fushi/", ""], ["t"]], ["member.duoxuanbuzhong_duozu", ["/member/duoxuanbuzhong/duozu/", ""], ["t"]], ["member.duoxuanzhongyi_fushi", ["/member/duoxuanzhongyi/fushi/", ""], ["t"]], ["member.duoxuanzhongyi_duozu", ["/member/duoxuanzhongyi/duozu/", ""], ["t"]], ["member.shengxiaolian_tuotou", ["/member/shengxiaolian/tuotou/", ""], ["t"]], ["member.shengxiaolian_fushi", ["/member/shengxiaolian/fushi/", ""], ["t"]], ["master.daily_general_agent", ["/master/general_agent/daily/", ""], ["general_agent_id"]], ["master.default_odds_zhengmate", ["/master/default_odds/zhengmate/", ""], ["t"]], ["master.shareholder_waidiao", ["/master/shareholder/waidiao/", ""], ["shareholder_id"]], ["member.tepingzhong_fushi", ["/member/tepingzhong/fushi/", ""], ["t"]], ["member.tepingzhong_duozu", ["/member/tepingzhong/duozu/", ""], ["t"]], ["master.daily_shareholder", ["/master/shareholder/daily/", ""], ["shareholder_id"]], ["master.statistics_shengxiaolian", ["/master/statistics/shengxiaolian/", ""], ["t"]], ["master.statistics_tepingzhong", ["/master/statistics/tepingzhong/", ""], ["t"]], ["master.statistics_weishulian", ["/master/statistics/weishulian/", ""], ["t"]], ["master.statistics_zhengmate", ["/master/statistics/zhengmate/", ""], ["t"]], ["master.statistics_buzhong", ["/master/statistics/buzhong/", ""], ["t"]], ["master.statistics_zhongyi", ["/master/statistics/zhongyi/", ""], ["t"]], ["member.weishulian_tuotou", ["/member/weishulian/tuotou/", ""], ["t"]], ["master.statistics_lianma", ["/master/statistics/lianma/", ""], ["t"]], ["member.weishulian_fushi", ["/member/weishulian/fushi/", ""], ["t"]], ["master.delete_lottery", ["/master/lotteries/delete/", ""], ["lottery_id"]], ["master.edit_lottery", ["/master/lotteries/edit/", ""], ["lottery_id"]], ["master.delete_handicap", ["/master/handicap/delete/", ""], ["handicap_id"]], ["master.close_handicap", ["/master/handicap/close/", ""], ["handicap_id"]], ["master.monitor_tepingzhong", ["/master/monitor/tepingzhong/", ""], ["t"]], ["master.monitor_zhengmate", ["/master/monitor/zhengmate/", ""], ["t"]], ["master.monitor_buzhong", ["/master/monitor/buzhong/", ""], ["t"]], ["master.monitor_zhongyi", ["/master/monitor/zhongyi/", ""], ["t"]], ["master.company_waidiao", ["/master/company/waidiao/", ""], ["company_id"]], ["master.general_waidiao", ["/master/general/waidiao/", ""], ["general_agent_id"]], ["master.monitor_zhengma", ["/master/monitor/zhengma/", ""], ["t"]], ["master.monitor_lianma", ["/master/monitor/lianma/", ""], ["t"]], ["master.system_marquee_delete", ["/master/marquee/delete/", ""], ["marquee_id"]], ["master.daily_company", ["/master/company/daily/", ""], ["company_id"]], ["master.monitor_tema", ["/master/monitor/tema/", ""], ["t"]], ["member.lianma_shengxiaoduipeng", ["/member/lianma/shengxiaoduipeng/", ""], ["t"]], ["member.lianma_shengweiduipeng", ["/member/lianma/shengweiduipeng/", ""], ["t"]], ["master.system_backup_download", ["/master/system/download_backup/", ""], ["backup_id"]], ["master.system_odds_difference", ["/master/system/odds_difference/", ""], ["idx"]], ["member.lianma_weishuduipeng", ["/member/lianma/weishuduipeng/", ""], ["t"]], ["member.lianma_renyiduipeng", ["/member/lianma/renyiduipeng/", ""], ["t"]], ["master.system_jiangshui", ["/master/system/jiangshui/", ""], ["idx"]], ["master.system_marquee", ["/master/system/marquee/", ""], ["marquee_id"]], ["master.system_logging", ["/master/system/logging/", ""], ["page"]], ["master.branch_waidiao", ["/master/branch/waidiao/", ""], ["branch_id"]], ["master.master_waidiao_detail", ["/master/master/waidiao/", ""], ["member_id"]], ["member.lianma_tuotou", ["/member/lianma/tuotou/", ""], ["t"]], ["master.system_backup", ["/master/system/backup/", ""], ["page"]], ["member.lianma_fushi", ["/member/lianma/fushi/", ""], ["t"]], ["master.daily_branch", ["/master/branch/daily/", ""], ["branch_id"]], ["master.bet", ["/master/system/bet/", ""], ["page"]], ["master.zero_clearing", ["/master/users/zero_clearing/", ""], ["member_id"]], ["master.users_general_agent", ["/master/users/general_agent/", ""], ["page"]], ["master.users_sub_accounts", ["/master/users/sub_accounts/", ""], ["page"]], ["master.users_shareholder", ["/master/users/shareholder/", ""], ["page"]], ["master.users_transfer", ["/master/users/transfers/", ""], ["page"]], ["master.users_company", ["/master/users/company/", ""], ["page"]], ["master.linkage", ["/master/users/linkage/", ""], ["member_id"]], ["master.agent_waidiao", ["/master/agent/waidiao/", ""], ["agent_id"]], ["master.users_branch", ["/master/users/branch/", ""], ["page"]], ["master.users_delete", ["/master/users/delete/", ""], ["member_id"]], ["master.users_groups", ["/master/users/groups/", ""], ["group_id"]], ["master.daily_detail", ["/master/daily/detail/", ""], ["member_id"]], ["master.users_agent", ["/master/users/agent/", ""], ["page"]], ["master.daily_agent", ["/master/agent/daily/", ""], ["agent_id"]], ["master.users_user", ["/master/users/user/", ""], ["page"]], ["agent.sub_accounts_delete", ["/agent/sub_accounts/delete/", ""], ["sub_account_id"]], ["agent.sub_accounts_edit", ["/agent/sub_accounts/edit/", ""], ["sub_account_id"]], ["agent.zhengmate_quick_transfer", ["/agent/statistics/zhengmate_quick_transfer/", ""], ["t"]], ["agent.statistics_shengxiaolian", ["/agent/statistics/shengxiaolian/", ""], ["t"]], ["agent.statistics_tepingzhong", ["/agent/statistics/tepingzhong/", ""], ["t"]], ["agent.statistics_weishulian", ["/agent/statistics/weishulian/", ""], ["t"]], ["agent.statistics_zhengmate", ["/agent/statistics/zhengmate/", ""], ["t"]], ["agent.statistics_buzhong", ["/agent/statistics/buzhong/", ""], ["t"]], ["agent.statistics_zhongyi", ["/agent/statistics/zhongyi/", ""], ["t"]], ["agent.statistics_lianma", ["/agent/statistics/lianma/", ""], ["t"]], ["agent.zero_clearing", ["/agent/users/zero_clearing/", ""], ["member_id"]], ["agent.users_general_agent", ["/agent/users/general_agent/", ""], ["page"]], ["agent.users_shareholder", ["/agent/users/shareholder/", ""], ["page"]], ["agent.linkage", ["/agent/users/linkage/", ""], ["member_id"]], ["agent.daily_detail", ["/agent/daily/detail/", ""], ["page"]], ["agent.users_branch", ["/agent/users/branch/", ""], ["page"]], ["agent.users_agent", ["/agent/users/agent/", ""], ["page"]], ["god.announcements_delete", ["/god/announcements/delete/", ""], ["announcement_id"]], ["realtime.duoxuanbuzhong", ["/realtime/duoxuanbuzhong/", ""], ["t"]], ["realtime.duoxuanzhongyi", ["/realtime/duoxuanzhongyi/", ""], ["t"]], ["realtime.shengxiaolian", ["/realtime/shengxiaolian/", ""], ["t"]], ["realtime.tepingzhong", ["/realtime/tepingzhong/", ""], ["t"]], ["realtime.weishulian", ["/realtime/weishulian/", ""], ["t"]], ["realtime.zhengmate", ["/realtime/zhengmate/", ""], ["t"]], ["realtime.zhengma", ["/realtime/zhengma/", ""], ["t"]], ["realtime.tema", ["/realtime/tema/", ""], ["t"]], ["master.taskstatus", ["/master/taskstatus/", ""], ["task_id"]], ["member.zhengmate", ["/member/zhengmate/", ""], ["t"]], ["master.lotteries", ["/master/lotteries/", ""], ["page"]], ["master.transfers_member", ["/master/transfers/", ""], ["member_id"]], ["master.closing", ["/master/closing/", ""], ["lottery_id"]], ["master.showing", ["/master/showing/", ""], ["lottery_id"]], ["master.billing", ["/master/billing/", ""], ["page"]], ["member.zhengma", ["/member/zhengma/", ""], ["t"]], ["member.daily", ["/member/daily/", ""], ["page"]], ["member.tema", ["/member/tema/", ""], ["t"]], ["agent.delete_member", ["/agent/delete_member/", ""], ["member_id"]], ["agent.change_member", ["/agent/change_member/", ""], ["member_id"]], ["agent.sub_accounts", ["/agent/sub_accounts/", ""], ["page"]], ["agent.change_agent", ["/agent/change_agent/", ""], ["member_id"]], ["agent.checkout", ["/agent/checkout/", ""], ["page"]], ["agent.members", ["/agent/members/", ""], ["page"]], ["agent.agents", ["/agent/agents/", ""], ["page"]], ["god.directors", ["/god/directors/", ""], ["page"]], ["god.dismiss", ["/god/dismiss/", ""], ["member_id"]], ["god.masters", ["/god/masters/", ""], ["page"]], ["god.report", ["/god/reports/", ""], ["t"]], ["site.change_skin", ["/change_skin/", ""], ["skin"]], ["static", ["/static/", ""], ["filename"]], ["master.statistics_qita_yixiaobuzhong", ["/master/statistics/qita/yixiaobuzhong"], []], ["master.statistics_qita_weishubuzhong", ["/master/statistics/qita/weishubuzhong"], []], ["master.statistics_qita_yixiaoliang", ["/master/statistics/qita/yixiaoliang"], []], ["master.statistics_qita_weishuliang", ["/master/statistics/qita/weishuliang"], []], ["master.statistics_qita_wuxing", ["/master/statistics/qita/wuxing"], []], ["master.statistics_qita_qima", ["/master/statistics/qita/qima"], []], ["master.monitor_yixiao_weishu_single_transfer", ["/master/monitor/yixiao_weishu/single_transfer"], []], ["master.monitor_shengxiaolian_single_transfer", ["/master/monitor/shengxiaolian/single_transfer"], []], ["master.monitor_yixiao_weishu_single_odd", ["/master/monitor/yixiao_weishu/single_odd"], []], ["master.monitor_shengxiaolian_single_odd", ["/master/minitor/shengxiaolian/single_odd"], []], ["master.monitor_shengxiaolian_waidiao", ["/master/monitor/shengxiaolian/waidiao"], []], ["master.monitor_tepingzhong_single_transfer", ["/master/monitor/tepingzhong/single_transfer"], []], ["master.monitor_tepingzhong_single_odd", ["/master/minitor/tepingzhong/single_odd"], []], ["master.monitor_tepingzhong_waidiao", ["/master/monitor/tepingzhong/waidiao"], []], ["master.monitor_weishulian_single_transfer", ["/master/monitor/weishulian/single_transfer"], []], ["master.monitor_weishulian_single_odd", ["/master/minitor/weishulian/single_odd"], []], ["master.monitor_weishulian_waidiao", ["/master/monitor/weishulian/waidiao"], []], ["master.monitor_zhengmate_single_transfer", ["/master/monitor/zhengmate/single_transfer"], []], ["master.monitor_zhengmate_single_odd", ["/master/monitor/zhengmate/single_odd"], []], ["master.monitor_zhengmate_same_odd", ["/master/monitor/zhengmate/same_odd"], []], ["master.monitor_transfer_shengxiaolian", ["/master/monitor/transfer/shengxiaolian"], []], ["master.monitor_transfer_weishulian", ["/master/monitor/transfer/weishulian"], []], ["master.monitor_zhengma_single_transfer", ["/master/monitor/zhengma/single_transfer"], []], ["master.monitor_liuxiao_single_transfer", ["/master/monitor/liuxiao/single_transfer"], []], ["master.monitor_buzhong_single_transfer", ["/master/monitor/buzhong/single_transfer"], []], ["master.monitor_zhongyi_single_transfer", ["/master/monitor/zhongyi/single_transfer"], []], ["master.monitor_zhengma_single_odd", ["/master/monitor/zhengma/single_odd"], []], ["master.monitor_liuxiao_single_odd", ["/master/minitor/liuxiao/single_odd"], []], ["master.monitor_buzhong_single_odd", ["/master/minitor/buzhong/single_odd"], []], ["master.monitor_zhongyi_single_odd", ["/master/minitor/zhongyi/single_odd"], []], ["master.monitor_zhengma_same_odd", ["/master/monitor/zhengma/same_odd"], []], ["master.monitor_buzhong_waidiao", ["/master/monitor/buzhong/waidiao"], []], ["master.monitor_zhongyi_waidiao", ["/master/monitor/zhongyi/waidiao"], []], ["master.monitor_lianma_single_transfer", ["/master/monitor/lianma/single_transfer"], []], ["master.monitor_lianma_single_odd2", ["/master/monitor/lianma/single_odd2"], []], ["master.monitor_lianma_single_odd", ["/master/monitor/lianma/single_odd"], []], ["master.monitor_lianma_wadiao", ["/master/monitor/lianma/waidiao"], []], ["master.monitor_tema_single_transfer", ["/master/monitor/tema/single_transfer"], []], ["master.monitor_qita_single_transfer", ["/master/monitor/qita/single_transfer"], []], ["master.monitor_tema_single_odd", ["/master/monitor/tema/single_odd"], []], ["master.monitor_qita_single_odd", ["/master/monitor/qita/single_odd"], []], ["master.monitor_tema_same_odd", ["/master/monitor/tema/same_odd"], []], ["master.system_odds_maximum", ["/master/system/odds/maximum"], []], ["master.bet_shengxiaolian", ["/master/system/bet/shengxiaolian"], []], ["master.bet_tepingzhong", ["/master/system/bet/tepingzhong"], []], ["master.bet_weishulian", ["/master/system/bet/weishulian"], []], ["master.bet_zhengmate", ["/master/system/bet/zhengmate"], []], ["master.bet_liangmian", ["/master/system/bet/liangmian"], []], ["master.bet_zhengma", ["/master/system/bet/zhengma"], []], ["master.bet_liuxiao", ["/master/system/bet/liuxiao"], []], ["master.bet_buzhong", ["/master/system/bet/buzhong"], []], ["master.bet_zhongyi", ["/master/system/bet/zhongyi"], []], ["master.bet_yixiao", ["/master/system/bet/yixiao"], []], ["master.bet_weishu", ["/master/system/bet/weishu"], []], ["master.bet_lianma", ["/master/system/bet/lianma"], []], ["master.bet_tema", ["/master/system/bet/tema"], []], ["master.bet_qita", ["/master/system/bet/qita"], []], ["master.bet_bose", ["/master/system/bet/bose"], []], ["master.users_general_agent_new", ["/master/users/general_agent/new"], []], ["master.users_sub_accounts_new", ["/master/users/sub_accounts/new"], []], ["master.users_shareholder_new", ["/master/users/shareholder/new"], []], ["master.users_transfer_new", ["/master/users/transfer/new"], []], ["master.users_company_new", ["/master/users/company/new"], []], ["master.users_branch_new", ["/master/users/branch/new"], []], ["master.users_level_update", ["/master/users/level/update"], []], ["master.users_agent_new", ["/master/users/agent/new"], []], ["master.users_user_new", ["/master/users/user/new"], []], ["agent.daily_general_agent_directly", ["/agent/general_agent/daily/directly"], []], ["agent.monthly2_shareholder_detail", ["/agent/shareholder/monthly2/detail"], []], ["agent.daily2_shareholder_detail", ["/agent/shareholder/daily2/detail"], []], ["agent.daily_shareholder_directly", ["/agent/shareholder/daily/directly"], []], ["agent.statistics_qita_yixiaobuzhong", ["/agent/statistics/qita/yixiaobuzhong"], []], ["agent.statistics_qita_weishubuzhong", ["/agent/statistics/qita/weishubuzhong"], []], ["agent.statistics_qita_yixiaoliang", ["/agent/statistics/qita/yixiaoliang"], []], ["agent.statistics_qita_weishuliang", ["/agent/statistics/qita/weishuliang"], []], ["agent.statistics_qita_texiao", ["/agent/statistics/qita/texiao"], []], ["agent.statistics_qita_wuxing", ["/agent/statistics/qita/wuxing"], []], ["agent.statistics_qita_banbo", ["/agent/statistics/qita/banbo"], []], ["agent.statistics_qita_qima", ["/agent/statistics/qita/qima"], []], ["agent.monthly2_company_detail", ["/agent/company/monthly2/detail"], []], ["agent.monthly2_general_detail", ["/agent/general/monthly2/detail"], []], ["agent.daily2_company_detail", ["/agent/company/daily2/detail"], []], ["agent.daily2_general_detail", ["/agent/general/daily2/detail"], []], ["agent.daily_company_directly", ["/agent/company/daily/directly"], []], ["agent.monthly2_branch_detail", ["/agent/branch/monthly2/detail"], []], ["agent.daily2_branch_detail", ["/agent/branch/daily2/detail"], []], ["agent.daily_branch_directly", ["/agent/branch/daily/directly"], []], ["agent.master_waidiao", ["/agent/master/agent/waidiao"], []], ["agent.users_general_agent_new", ["/agent/users/general_agent/new"], []], ["agent.users_shareholder_new", ["/agent/users/shareholder/new"], []], ["agent.monthly2_agent_detail", ["/agent/agent/monthly2/detail"], []], ["agent.daily2_agent_detail", ["/agent/agent/daily2/detail"], []], ["agent.users_branch_new", ["/agent/users/branch/new"], []], ["agent.users_level_update", ["/agent/users/level/update"], []], ["agent.users_agent_new", ["/agent/users/agent/new"], []], ["realtime.monitor_shengxiaolian", ["/realtime/monitor/monitor_shengxiaolian"], []], ["realtime.monitor_change_odds_mutiple", ["/realtime/monitor/change_odds_mutiple"], []], ["realtime.monitor_weishulian", ["/realtime/monitor/monitor_weishulian"], []], ["realtime.monitor_yixiao_weishu", ["/realtime/monitor/yixiao_weishu"], []], ["realtime.monitor_change_odds", ["/realtime/monitor/change_odds"], []], ["realtime.monitor_liuxiao", ["/realtime/monitor/liuxiao"], []], ["realtime.monitor_qita", ["/realtime/monitor/qita"], []], ["realtime.qita_yixiaobuzhong", ["/realtime/qita/yixiaobuzhong"], []], ["realtime.qita_weishubuzhong", ["/realtime/qita/weishubuzhong"], []], ["realtime.qita_yixiaoliang", ["/realtime/qita/yixiaoliang"], []], ["realtime.qita_weishuliang", ["/realtime/qita/weishuliang"], []], ["realtime.qita_texiao", ["/realtime/qita/texiao"], []], ["realtime.qita_wuxing", ["/realtime/qita/wuxing"], []], ["realtime.qita_banbo", ["/realtime/qita/banbo"], []], ["realtime.qita_qima", ["/realtime/qita/qima"], []], ["master.default_odds_liuxiao_buzhong_shengxiaolian_weishulian", ["/master/default_odds/liuxiao_buzhong_shengxiaolian_weishulian"], []], ["master.default_odds_xuanxuanzhongyi_tepingzhong", ["/master/default_odds/xuanxuanzhongyi_tepingzhong"], []], ["master.default_odds_shengxiaoliang", ["/master/default_odds/shengxiaoliang"], []], ["master.default_odds_yixiaobuzhong", ["/master/default_odds/yixiaobuzhong"], []], ["master.default_odds_weishubuzhong", ["/master/default_odds/weishubuzhong"], []], ["master.default_odds_weishuliang", ["/master/default_odds/weishuliang"], []], ["master.default_odds_zhengmate", ["/master/default_odds/zhengmate"], []], ["master.default_odds_zhengma", ["/master/default_odds/zhengma"], []], ["master.default_odds_lianma", ["/master/default_odds/lianma"], []], ["master.default_odds_texiao", ["/master/default_odds/texiao"], []], ["master.default_odds_yixiao", ["/master/default_odds/yixiao"], []], ["master.default_odds_weishu", ["/master/default_odds/weishu"], []], ["master.default_odds_wuxing", ["/master/default_odds/wuxing"], []], ["master.default_odds_banbo", ["/master/default_odds/banbo"], []], ["master.default_odds_tema", ["/master/default_odds/tema"], []], ["master.default_odds_qima", ["/master/default_odds/qima"], []], ["master.statistics_shengxiaolian", ["/master/statistics/shengxiaolian"], []], ["master.buhuo_lianma", ["/master/statistics/buhuo_lianma"], []], ["master.statistics_tepingzhong", ["/master/statistics/tepingzhong"], []], ["master.statistics_lianma_all", ["/master/statistics/lianma_all"], []], ["master.statistics_weishulian", ["/master/statistics/weishulian"], []], ["master.statistics_zheng1to6", ["/master/statistics/zheng1to6"], []], ["master.statistics_zhengmate", ["/master/statistics/zhengmate"], []], ["master.statistics_zhengma", ["/master/statistics/zhengma"], []], ["master.statistics_liuxiao", ["/master/statistics/liuxiao"], []], ["master.statistics_buzhong", ["/master/statistics/buzhong"], []], ["master.statistics_zhongyi", ["/master/statistics/zhongyi"], []], ["master.statistics_lianma", ["/master/statistics/lianma"], []], ["master.statistics_tema", ["/master/statistics/tema"], []], ["master.statistics_qita", ["/master/statistics/qita"], []], ["master.transfers_statistics", ["/master/transfers/statistics"], []], ["master.transfers_new", ["/master/transfers/new"], []], ["master.urgent_fengpan", ["/master/handicap/urgent_fengpan"], []], ["master.shoufengdan", ["/master/handicap/shoufengdan"], []], ["master.fengpan", ["/master/handicap/fengpan"], []], ["master.kaipan", ["/master/handicap/kaipan"], []], ["master.monthly2_master_detail", ["/master/monthly2/detail"], []], ["master.monitor_change_amount_odd", ["/master/monitor/change_amount_odd"], []], ["master.monitor_yixiao_weishu", ["/master/monitor/yixiao_weishu"], []], ["master.monitor_shengxiaolian", ["/master/monitor/shengxiaolian"], []], ["master.monitor_weishulian", ["/master/monitor/weishulian"], []], ["master.monitor_liuxiao", ["/master/monitor/liuxiao"], []], ["master.monitor_qita", ["/master/monitor/qita"], []], ["master.system_odds_difference", ["/master/system/odds_difference"], []], ["master.system_alarm", ["/master/system/system_alarm"], []], ["master.system_information", ["/master/system/information"], []], ["master.system_backup_now", ["/master/system/backup_now"], []], ["master.system_jiangshui", ["/master/system/jiangshui"], []], ["master.system_handicap", ["/master/system/handicap"], []], ["master.system_tuishui", ["/master/system/tuishui"], []], ["master.system_marquee", ["/master/system/marquee"], []], ["master.system_logging", ["/master/system/logging"], []], ["master.master_waidiao", ["/master/master/waidiao"], []], ["master.system_backup", ["/master/system/backup"], []], ["master.daily2_master_detail", ["/master/daily2/detail"], []], ["master.bet", ["/master/system/bet"], []], ["master.users_general_agent", ["/master/users/general_agent"], []], ["master.users_sub_accounts", ["/master/users/sub_accounts"], []], ["master.users_shareholder", ["/master/users/shareholder"], []], ["master.users_transfer", ["/master/users/transfers"], []], ["master.users_cleaning", ["/master/users/cleaning"], []], ["master.users_company", ["/master/users/company"], []], ["master.users_branch", ["/master/users/branch"], []], ["master.users_groups", ["/master/users/groups"], []], ["master.users_check", ["/master/users/check"], []], ["master.users_agent", ["/master/users/agent"], []], ["master.users_user", ["/master/users/user"], []], ["member.qita_yixiaobuzhong", ["/member/qita/yixiaobuzhong"], []], ["member.qita_weishubuzhong", ["/member/qita/weishubuzhong"], []], ["member.qita_yixiaoliang", ["/member/qita/yixiaoliang"], []], ["member.qita_weishuliang", ["/member/qita/weishuliang"], []], ["member.qita_texiao", ["/member/qita/texiao"], []], ["member.qita_wuxing", ["/member/qita/wuxing"], []], ["member.qita_banbo", ["/member/qita/banbo"], []], ["member.qita_qima", ["/member/qita/qima"], []], ["agent.general_monthly", ["/agent/general_agent/monthly"], []], ["agent.general_daily", ["/agent/general_agent/daily"], []], ["agent.sub_accounts_create", ["/agent/sub_accounts/create"], []], ["agent.shareholder_monthly2", ["/agent/shareholder/monthly2"], []], ["agent.shareholder_waidiao", ["/agent/shareholder/waidiao"], []], ["agent.shareholder_monthly", ["/agent/shareholder/monthly"], []], ["agent.shareholder_daily2", ["/agent/shareholder/daily2"], []], ["agent.shareholder_daily", ["/agent/shareholder/daily"], []], ["agent.zhengmate_quick_transfer", ["/agent/statistics/zhengmate_quick_transfer"], []], ["agent.zhengma_quick_transfer", ["/agent/statistics/zhengma_quick_transfer"], []], ["agent.tema_quick_transfer", ["/agent/statistics/tema_quick_transfer"], []], ["agent.statistics_yixiao_weishu", ["/agent/statistics/yixiao_weishu"], []], ["agent.change_ziliu", ["/agent/statistics/change_ziliu"], []], ["agent.statistics_zhengma", ["/agent/statistics/zhengma"], []], ["agent.statistics_liuxiao", ["/agent/statistics/liuxiao"], []], ["agent.statistics_tema", ["/agent/statistics/tema"], []], ["agent.statistics_qita", ["/agent/statistics/qita"], []], ["agent.statistics_all", ["/agent/statistics/all"], []], ["agent.company_monthly2", ["/agent/company/monthly2"], []], ["agent.general_monthly2", ["/agent/general/monthly2"], []], ["agent.company_waidiao", ["/agent/company/waidiao"], []], ["agent.general_waidiao", ["/agent/general/waidiao"], []], ["agent.company_monthly", ["/agent/company/monthly"], []], ["agent.create_member", ["/agent/members/create"], []], ["agent.company_daily2", ["/agent/company/daily2"], []], ["agent.general_daily2", ["/agent/general/daily2"], []], ["agent.company_daily", ["/agent/company/daily"], []], ["agent.branch_monthly2", ["/agent/branch/monthly2"], []], ["agent.branch_waidiao", ["/agent/branch/waidiao"], []], ["agent.branch_monthly", ["/agent/branch/monthly"], []], ["agent.monthly_master", ["/agent/master/monthly"], []], ["agent.branch_daily2", ["/agent/branch/daily2"], []], ["agent.branch_daily", ["/agent/branch/daily"], []], ["agent.daily_master", ["/agent/master/daily"], []], ["agent.users_general_agent", ["/agent/users/general_agent"], []], ["agent.users_shareholder", ["/agent/users/shareholder"], []], ["agent.agent_waidiao", ["/agent/agent/waidiao"], []], ["agent.daily_detail", ["/agent/daily/detail"], []], ["agent.users_branch", ["/agent/users/branch"], []], ["agent.users_check", ["/agent/users/check"], []], ["agent.users_agent", ["/agent/users/agent"], []], ["god.announcements_new", ["/god/announcements/new"], []], ["god.masters_new", ["/god/masters/new"], []], ["realtime.latest_ten_gambles", ["/realtime/latest_ten_gambles"], []], ["realtime.master_messages", ["/realtime/master_messages"], []], ["realtime.yixiao_weishu", ["/realtime/yixiao_weishu"], []], ["realtime.liuxiao", ["/realtime/liuxiao"], []], ["member.duoxuanbuzhong", ["/member/duoxuanbuzhong"], []], ["member.duoxuanzhongyi", ["/member/duoxuanzhongyi"], []], ["member.init_password", ["/member/init_password"], []], ["member.yixiao_weishu", ["/member/yixiao_weishu"], []], ["member.shengxiaolian", ["/member/shengxiaolian"], []], ["master.init_password", ["/master/init_password"], []], ["master.monitor_first", ["/master/monitor_first"], []], ["master.data_cleaning", ["/master/data_cleaning"], []], ["master.default_odds", ["/master/default_odds"], []], ["member.custom_info", ["/member/custom_info"], []], ["member.quick_order", ["/member/quick_order"], []], ["member.tepingzhong", ["/member/tepingzhong"], []], ["member.weishulian", ["/member/weishulian"], []], ["master.statistics", ["/master/statistics"], []], ["master.lotteries", ["/master/lotteries"], []], ["master.transfers", ["/master/transfers"], []], ["member.password", ["/member/password"], []], ["member.omission", ["/member/omission"], []], ["master.password", ["/master/password"], []], ["master.handicap", ["/master/handicap"], []], ["master.monthly2_master", ["/master/monthly2"], []], ["master.omission", ["/master/omission"], []], ["member.welcome", ["/member/welcome"], []], ["member.liuxiao", ["/member/liuxiao"], []], ["member.history", ["/member/history"], []], ["member.profile", ["/member/profile"], []], ["master.drawing", ["/master/drawing"], []], ["master.monitor", ["/master/monitor"], []], ["master.billing", ["/master/billing"], []], ["master.monthly_master", ["/master/monthly"], []], ["member.logout", ["/member/logout"], []], ["member.lianma", ["/member/lianma"], []], ["member.result", ["/member/result"], []], ["master.logout", ["/master/logout"], []], ["master.system", ["/master/system"], []], ["master.daily2_master", ["/master/daily2"], []], ["member.login", ["/member/login"], []], ["member.menu2", ["/member/menu2"], []], ["member.daily", ["/member/daily"], []], ["master.login", ["/master/login"], []], ["master.users", ["/master/users"], []], ["master.daily_master", ["/master/daily"], []], ["member.top2", ["/member/top2"], []], ["member.qita", ["/member/qita"], []], ["member.rule", ["/member/rule"], []], ["master.rule", ["/master/rule"], []], ["member.top", ["/member/top"], []], ["agent.init_password", ["/agent/init_password"], []], ["agent.buhuo_mutiple", ["/agent/buhuo_mutiple"], []], ["agent.sub_accounts", ["/agent/sub_accounts"], []], ["agent.create_agent", ["/agent/create_agent"], []], ["agent.open_lottery", ["/agent/open_lottery"], []], ["agent.buhuo_lianma", ["/agent/buhuo_lianma"], []], ["agent.statistics", ["/agent/statistics"], []], ["agent.dashboard", ["/agent/dashboard"], []], ["agent.password", ["/agent/password"], []], ["agent.checkout", ["/agent/checkout"], []], ["agent.monthly2", ["/agent/monthly2"], []], ["agent.omission", ["/agent/omission"], []], ["agent.members", ["/agent/members"], []], ["agent.profile", ["/agent/profile"], []], ["agent.monthly", ["/agent/monthly"], []], ["agent.logout", ["/agent/logout"], []], ["agent.agents", ["/agent/agents"], []], ["agent.daily2", ["/agent/daily2"], []], ["agent.login", ["/agent/login"], []], ["agent.daily", ["/agent/daily"], []], ["agent.buhuo", ["/agent/buhuo"], []], ["agent.rule", ["/agent/rule"], []], ["god.announcements", ["/god/announcements"], []], ["god.directors", ["/god/directors"], []], ["god.password", ["/god/password"], []], ["god.welcome", ["/god/welcome"], []], ["god.masters", ["/god/masters"], []], ["god.report", ["/god/reports"], []], ["god.logout", ["/god/logout"], []], ["god.login", ["/god/login"], []], ["god.merge", ["/god/merge"], []], ["serve_js", ["/jsglue.js"], []], ["site.captcha", ["/captcha"], []], ["member.index", ["/member/"], []], ["master.index", ["/master/"], []], ["agent.index", ["/agent/"], []], ["god.index", ["/god/"], []], ["site.redirect_to_master", ["/m"], []], ["site.index", ["/"], []], ["member.duoxuanbuzhong_fushi", ["/member/duoxuanbuzhong/fushi"], []], ["member.duoxuanbuzhong_duozu", ["/member/duoxuanbuzhong/duozu"], []], ["member.duoxuanzhongyi_fushi", ["/member/duoxuanzhongyi/fushi"], []], ["member.duoxuanzhongyi_duozu", ["/member/duoxuanzhongyi/duozu"], []], ["member.shengxiaolian_tuotou", ["/member/shengxiaolian/tuotou"], []], ["member.shengxiaolian_fushi", ["/member/shengxiaolian/fushi"], []], ["member.tepingzhong_fushi", ["/member/tepingzhong/fushi"], []], ["member.tepingzhong_duozu", ["/member/tepingzhong/duozu"], []], ["member.weishulian_tuotou", ["/member/weishulian/tuotou"], []], ["member.weishulian_fushi", ["/member/weishulian/fushi"], []], ["master.monitor_tepingzhong", ["/master/monitor/tepingzhong"], []], ["master.monitor_zhengmate", ["/master/monitor/zhengmate"], []], ["master.monitor_zhengma", ["/master/monitor/zhengma"], []], ["master.monitor_buzhong", ["/master/monitor/buzhong"], []], ["master.monitor_zhongyi", ["/master/monitor/zhongyi"], []], ["master.monitor_lianma", ["/master/monitor/lianma"], []], ["master.monitor_tema", ["/master/monitor/tema"], []], ["member.lianma_shengxiaoduipeng", ["/member/lianma/shengxiaoduipeng"], []], ["member.lianma_shengweiduipeng", ["/member/lianma/shengweiduipeng"], []], ["member.lianma_weishuduipeng", ["/member/lianma/weishuduipeng"], []], ["member.lianma_renyiduipeng", ["/member/lianma/renyiduipeng"], []], ["member.lianma_tuotou", ["/member/lianma/tuotou"], []], ["member.lianma_fushi", ["/member/lianma/fushi"], []], ["agent.statistics_shengxiaolian", ["/agent/statistics/shengxiaolian"], []], ["agent.statistics_tepingzhong", ["/agent/statistics/tepingzhong"], []], ["agent.statistics_weishulian", ["/agent/statistics/weishulian"], []], ["agent.statistics_zhengmate", ["/agent/statistics/zhengmate"], []], ["agent.statistics_buzhong", ["/agent/statistics/buzhong"], []], ["agent.statistics_zhongyi", ["/agent/statistics/zhongyi"], []], ["agent.statistics_lianma", ["/agent/statistics/lianma"], []], ["member.zhengmate", ["/member/zhengmate"], []], ["member.zhengma", ["/member/zhengma"], []], ["member.tema", ["/member/tema"], []]],
    'url_for': function (endpoint, rule) {
      if (typeof rule === "undefined") rule = {};

      var has_everything = false,
        url = "";

      var is_absolute = false,
        has_anchor = false,
        has_scheme;
      var anchor = "",
        scheme = "";

      if (rule['_external'] === true) {
        is_absolute = true;
        scheme = location.protocol.split(':')[0];
        delete rule['_external'];
      }

      if ('_scheme' in rule) {
        if (is_absolute) {
          scheme = rule['_scheme'];
          delete rule['_scheme'];
        } else {
          throw {
            name: "ValueError",
            message: "_scheme is set without _external."
          };
        }
      }

      if ('_anchor' in rule) {
        has_anchor = true;
        anchor = rule['_anchor'];
        delete rule['_anchor'];
      }

      for (var i in this._endpoints) {
        if (endpoint == this._endpoints[i][0]) {
          var url = '';
          var j = 0;
          var has_everything = true;
          var used = {};
          for (var j = 0; j < this._endpoints[i][2].length; j++) {
            var t = rule[this._endpoints[i][2][j]];
            if (typeof t === "undefined") {
              has_everything = false;
              break;
            }
            url += this._endpoints[i][1][j] + t;
            used[this._endpoints[i][2][j]] = true;
          }
          if (has_everything) {
            if (this._endpoints[i][2].length != this._endpoints[i][1].length)
              url += this._endpoints[i][1][j];

            var first = true;
            for (var r in rule) {
              if (r[0] != '_' && !(r in used)) {
                if (first) {
                  url += '?';
                  first = false;
                } else {
                  url += '&';
                }
                url += r + '=' + encodeURI(rule[r]);
              }
            }
            if (has_anchor) {
              url += "#" + anchor;
            }

            if (is_absolute) {
              return scheme + "://" + location.host + url;
            } else {
              return url;
            }
          }
        }
      }

      throw {
        name: 'BuildError',
        message: "Couldn't find the matching endpoint."
      };
    }
  };
});