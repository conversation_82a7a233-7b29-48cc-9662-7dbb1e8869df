.loading-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.8);
    z-index: 200000;
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.4s;
}

.loading-wrapper.active {
    visibility: visible;
    opacity: 1;
}

.loading-wrapper.active .loading {
    display: block;
    width: 150px;
    height: 150px;
}

@-webkit-keyframes rotate {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    50% {
        -webkit-transform: rotate(60deg);
        transform: rotate(60deg);
    }
    100% {
        -webkit-transform: rotate(120deg);
        transform: rotate(120deg);
    }
}

@keyframes rotate {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    50% {
        -webkit-transform: rotate(60deg);
        transform: rotate(60deg);
    }
    100% {
        -webkit-transform: rotate(120deg);
        transform: rotate(120deg);
    }
}

.loading .bar {
    bottom: 50%;
    left: calc(50%);
    position: absolute;
    height: 100px;
    width: 0;
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%;
}

.loading .dot {
    position: absolute;
    bottom: 20px;
    left: -1px;
    width: 2px;
    height: 18px;
    background-color: #fff;
    border-radius: 2px;
}

.loading .bar:nth-child(1) {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
}

.loading .bar:nth-child(1) .dot {
    -webkit-animation: roll 1s infinite alternate;
    animation: roll 1s infinite alternate;
    -webkit-animation-delay: -0s;
    animation-delay: -0s;
}

.loading .bar:nth-child(2) {
    -webkit-transform: rotate(30deg);
    transform: rotate(30deg);
}

.loading .bar:nth-child(2) .dot {
    -webkit-animation: roll 1s infinite alternate;
    animation: roll 1s infinite alternate;
    -webkit-animation-delay: -0.8333333333s;
    animation-delay: -0.8333333333s;
}

.loading .bar:nth-child(3) {
    -webkit-transform: rotate(60deg);
    transform: rotate(60deg);
}

.loading .bar:nth-child(3) .dot {
    -webkit-animation: roll 1s infinite alternate;
    animation: roll 1s infinite alternate;
    -webkit-animation-delay: -1.6666666667s;
    animation-delay: -1.6666666667s;
}

.loading .bar:nth-child(4) {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
}

.loading .bar:nth-child(4) .dot {
    -webkit-animation: roll 1s infinite alternate;
    animation: roll 1s infinite alternate;
    -webkit-animation-delay: -2.5s;
    animation-delay: -2.5s;
}

.loading .bar:nth-child(5) {
    -webkit-transform: rotate(120deg);
    transform: rotate(120deg);
}

.loading .bar:nth-child(5) .dot {
    -webkit-animation: roll 1s infinite alternate;
    animation: roll 1s infinite alternate;
    -webkit-animation-delay: -3.3333333333s;
    animation-delay: -3.3333333333s;
}

.loading .bar:nth-child(6) {
    -webkit-transform: rotate(150deg);
    transform: rotate(150deg);
}

.loading .bar:nth-child(6) .dot {
    -webkit-animation: roll 1s infinite alternate;
    animation: roll 1s infinite alternate;
    -webkit-animation-delay: -4.1666666667s;
    animation-delay: -4.1666666667s;
}

.loading .bar:nth-child(7) {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
}

.loading .bar:nth-child(7) .dot {
    -webkit-animation: roll 1s infinite alternate;
    animation: roll 1s infinite alternate;
    -webkit-animation-delay: -5s;
    animation-delay: -5s;
}

.loading .bar:nth-child(8) {
    -webkit-transform: rotate(210deg);
    transform: rotate(210deg);
}

.loading .bar:nth-child(8) .dot {
    -webkit-animation: roll 1s infinite alternate;
    animation: roll 1s infinite alternate;
    -webkit-animation-delay: -5.8333333333s;
    animation-delay: -5.8333333333s;
}

.loading .bar:nth-child(9) {
    -webkit-transform: rotate(240deg);
    transform: rotate(240deg);
}

.loading .bar:nth-child(9) .dot {
    -webkit-animation: roll 1s infinite alternate;
    animation: roll 1s infinite alternate;
    -webkit-animation-delay: -6.6666666667s;
    animation-delay: -6.6666666667s;
}

.loading .bar:nth-child(10) {
    -webkit-transform: rotate(270deg);
    transform: rotate(270deg);
}

.loading .bar:nth-child(10) .dot {
    -webkit-animation: roll 1s infinite alternate;
    animation: roll 1s infinite alternate;
    -webkit-animation-delay: -7.5s;
    animation-delay: -7.5s;
}

.loading .bar:nth-child(11) {
    -webkit-transform: rotate(300deg);
    transform: rotate(300deg);
}

.loading .bar:nth-child(11) .dot {
    -webkit-animation: roll 1s infinite alternate;
    animation: roll 1s infinite alternate;
    -webkit-animation-delay: -8.3333333333s;
    animation-delay: -8.3333333333s;
}

.loading .bar:nth-child(12) {
    -webkit-transform: rotate(330deg);
    transform: rotate(330deg);
}

.loading .bar:nth-child(12) .dot {
    -webkit-animation: roll 1s infinite alternate;
    animation: roll 1s infinite alternate;
    -webkit-animation-delay: -9.1666666667s;
    animation-delay: -9.1666666667s;
}

.loading .bar:nth-child(2n) .dot {
    -webkit-animation-delay: 0.2s;
    animation-delay: 0.2s;
    height: 10px;
}

@-webkit-keyframes roll {
    from {
        -webkit-transform: scale(1, 0.1);
        transform: scale(1, 0.1);
        -webkit-transform-origin: 50% 80%;
        transform-origin: 50% 80%;
        opacity: 0.2;
    }
    to {
        -webkit-transform: scale(1, 1);
        transform: scale(1, 1);
        -webkit-transform-origin: 50% 100%;
        transform-origin: 50% 100%;
        opacity: 1;
    }
}

@keyframes roll {
    from {
        -webkit-transform: scale(1, 0.1);
        transform: scale(1, 0.1);
        -webkit-transform-origin: 50% 80%;
        transform-origin: 50% 80%;
        opacity: 0.2;
    }
    to {
        -webkit-transform: scale(1, 1);
        transform: scale(1, 1);
        -webkit-transform-origin: 50% 100%;
        transform-origin: 50% 100%;
        opacity: 1;
    }
}