.margintop5 {
	margin-top: 5px;
}

.margintop7 {
	margin-top: 7px;
}

.margintop8 {
	margin-top: 8px;
}
.margintop10 {
	margin-top: 10px;
}
.margintop15 {
	margin-top: 15px;
}
.margintop20 {
	margin-top: 20px;
}
.margintop25 {
	margin-top: 25px;
}
.margintop30 {
	margin-top: 30px;
}
.margintop35 {
	margin-top: 35px;
}
.margintop40{
	margin-top: 40px;
}
.margintop50{
	margin-top: 50px;
}


.marginbtm5 {
	margin-bottom: 5px;
}

.marginbtm7 {
	margin-bottom: 7px;
}
.marginbtm8 {
	margin-bottom: 8px;
}
.marginbtm10 {
	margin-bottom: 10px;
}
.marginbtm15 {
	margin-bottom: 15px;
}
.marginbtm20 {
	margin-bottom: 20px;
}
.marginbtm25 {
	margin-bottom: 25px;
}
.marginbtm30 {
	margin-bottom: 30px;
}
.marginbtm35 {
	margin-bottom: 35px;
}
.marginbtm40{
	margin-bottom: 40px;
}

.marginleft3 {
	margin-left: 3px;
}
.marginleft5 {
	margin-left: 5px;
}

.marginleft7 {
	margin-left: 7px;
}

.marginleft10 {
	margin-left: 10px;
}

.marginleft15 {
	margin-left: 15px;
}

.marginleft20 {
	margin-left: 20px;
}

.marginleft25 {
	margin-left: 25px;
}

.marginleft30 {
	margin-left: 30px;
}

.marginright5 {
	margin-right: 5px;
}

.marginright7 {
	margin-right: 7px;
}

.marginright9 {
	margin-right: 9px;
}

.marginright10 {
	margin-right: 10px;
}

.marginright15 {
	margin-right: 15px;
}

.marginright20 {
	margin-right: 20px;
}

.marginright25 {
	margin-right: 25px;
}

.marginright30 {
	margin-right: 30px;
}

.alignleft {
	text-align: left;
}

.alignright {
	text-align: right;
}

.aligncenter {
	text-align: center;	
}

.floatleft {
	float: left;
}

.floatright {
	float: right;
}

.blue {
	color: #217eec;
}

.red {
	color: #FB0000;
}

.gray {
	color: #a2a2a2;
}

.smalltxt {
	font-size: 12px;
}

.bigtxt {
	font-size: 24px;	
}

.pointer {
	cursor: pointer;
}

.gap5 {
	height: 10px;
	width: 100%;
}

.bold{
	font-weight: bold;
}