/* login */
.login-actiyity .wrap .flash-roll, .wrap .roll-container {
	left: 50%;
	-webkit-transform: translate(-50%, 0);
}

.login-actiyity .wrap .btn-roll, .wrap .flash-roll, .wrap .roll-container {
	-webkit-transform: translate(-50%, 0);
}

.login-actiyity.pane  {
	background: #c6257c;
}

.login-actiyity .bar.bar-activity {background: rgb(198, 37, 124)!important}

.login-actiyity .bg {
	background: #c02f27 url(../images/activity/login/bg.png) no-repeat;
	-webkit-background-size: 100% auto;
	height: 100%;
}

.login-actiyity .wrap {
	position: relative;
	padding-top: 40%;
	height: 70%;
}

.login-actiyity .wrap .box-roll {
	margin: 0 auto;
	position: relative;
}

.login-actiyity .wrap .box-roll img {
	width: 100%;
}

.login-actiyity .wrap .flash-roll {
	position: relative;
	width: 90%;
}

.login-actiyity .wrap .roll-container {
	position: absolute;
	width: 60%;
	bottom: 3%;
}

.login-actiyity .wrap .back-roll {
	position: relative;
	font-size: 0;
}

.login-actiyity .wrap .back-roll img {
	display: block;
}

.login-actiyity .wrap .roll-img {
	position: absolute;
	top: 0;
	left: 0;
}

.login-actiyity .wrap .btn-roll {
	position: absolute;
	width: 18%;
	left: 50%;
	top: 25%;
}

.login-actiyity .wrap .showbar {
	width: 42%;
	margin: 10px auto;
	position: relative;
	font-size: 12px;
}

.login-actiyity .wrap .showbar img {
	width: 100%;
}

.login-actiyity .wrap .showbar .text {
	position: absolute;
	color: #ea4c4c;
	height: 100%;
	width: 100%;
	left: 0;
	top: 2%;
	text-align: center;
}

.login-actiyity .wrap .bottom {
	width: 76%;
	margin: 0 auto 20px;
	color: #ffe5aa;
	font-size: 12px;
}

.login-actiyity .wrap .bottom .tit {
	font-weight: 700;
	text-indent: 0;
	margin-top: .2em;
}

.login-actiyity .wrap .bottom li {
	text-indent: 1em;
}

/* cont */

.cont-actiyity .bg {
    background: url(../images/activity/cont/bg.png) top no-repeat;
    -webkit-background-size: cover;
    padding-bottom: 30px;
}

.cont-actiyity .bg img {
    width: 100%;
    display: block;
}

.cont-actiyity .header {
    position: relative;
}

.cont-actiyity .header .signdays {
    position: absolute;
    bottom: 20%;
    left: 50%;
    -webkit-transform: translateX(-50%);
    color: #c13800;
    font-size: 16px;
    background: url(../images/activity/cont/bg_signday.png) repeat-x;
    border-radius: 5px;
    overflow: hidden;
}

.cont-actiyity .header .signdays p {
    font-weight: 700;
    margin: 0;
    padding: 5px 10px;
    white-space: nowrap;
}

.cont-actiyity .gamebox {
    position: relative;
    margin-top: -30%;
    overflow: hidden;
}

.cont-actiyity .gamebox .light {
    -webkit-animation: turn360 30s linear infinite;
}

.cont-actiyity .gamebox .desk {
    position: absolute;
    width: 80%;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
}

.cont-actiyity .gamebox .egg {
    position: absolute;
    left: 40%;
    top: 35%;
    width: 20%;
}

.cont-actiyity .gamebox .egg.ani {
    -webkit-animation: rockegg 3s infinite;
}

.cont-actiyity .gamebox .hammer {
    position: absolute;
    left: 52%;
    top: 26%;
    width: 20%;
    display: none;
}

.cont-actiyity .gamebox .hammer.ani {
    display: block;
    -webkit-animation: rockhammer 1s infinite;
}

.cont-actiyity .award {
    margin: -10% auto 0;
    width: 90%;
    color: #fff;
    font-size: 12px;
}

.cont-actiyity .award p {
    margin: 0;
    line-height: 22px;
}

.cont-actiyity .award .tit {
    text-align: center;
    font-weight: 700;
    font-size: 16px;
    line-height: 30px;
}

@-webkit-keyframes turn360 {
    0% {
        -webkit-transform: rotate(0);
    }
    50% {
        -webkit-transform: rotate(180deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
    }
}

@-webkit-keyframes rockhammer {
    20% {
        -webkit-transform: rotate3d(0, 0, 1, 15deg);
    }
    40% {
        -webkit-transform: rotate3d(0, 0, 1, -10deg);
    }
    60% {
        -webkit-transform: rotate3d(0, 0, 1, 5deg);
    }
    80% {
        -webkit-transform: rotate3d(0, 0, 1, -5deg);
    }
    100% {
        -webkit-transform: rotate3d(0, 0, 1, 0deg);
    }
}

@-webkit-keyframes rockegg {
    0% {
        -webkit-transform: translateX(0);
    }
    6.5% {
        -webkit-transform: translateX(-6px) rotateY(-9deg);
    }
    18.5% {
        -webkit-transform: translateX(5px) rotateY(7deg);
    }
    31.5% {
        -webkit-transform: translateX(-3px) rotateY(-5deg);
    }
    43.5% {
        -webkit-transform: translateX(2px) rotateY(3deg);
    }
    50% {
        -webkit-transform: translateX(0);
    }
}
.round {
	box-shadow: inset 1px -6px 10px #fff;
	width: .9rem;
	height: .9rem;
	border-radius: .9rem;
	background: #d5d5d5;
	color: #000;
	margin-top: .3rem;
	margin-right: .1rem;
	line-height: .9rem;
	font-size: .6rem
}

.round,.round-1 {
	display: inline-block;
	text-align: center
}

.round-1 {
	width: 1.4rem;
	height: 1.4rem;
	border-radius: 1.4rem;
	font-size: .9rem;
	line-height: 1.4rem
}

.round-1,.round-2 {
	box-shadow: inset 1px -6px 10px #fff;
	background: #d5d5d5;
	color: #000!important
}

.round-2 {
	width: .95rem;
	height: .95rem;
	border-radius: .95rem;
	font-weight: lighter!important;
	margin-right: .1rem;
	margin-top: .2rem;
	font-size: .65rem;
	line-height: .95rem
}

.round-3 {
	width: 1.4rem;
	height: 1.4rem;
	margin-right: 1px;
	color: #000;
	font-size: .7rem;
	display: inline-block;
	text-align: center
}

.round-3.red {
	background-image: url("../images/ball/red.png");
	background-size: 1.4rem
}

.round-3.blue {
	background-image: url("../images/ball/blue.png");
	background-size: 1.4rem
}

.round-3.green {
	background-image: url("../images/ball/green.png");
	background-size: 1.4rem
}

.round-4 {
	width: 1.1rem;
	height: 1.1rem;
	color: #000;
	text-align: center;
	float: left;
	font-weight: bolder;
	margin-top: .2rem;
	margin-right: .1rem;
	line-height: 1rem
}

.round-4.red {
	background-image: url("../images/ball/red.png");
	background-size: 1.1rem
}

.round-4.blue {
	background-image: url("../images/ball/blue.png");
	background-size: 1.1rem
}

.round-4.green {
	background-image: url("../images/ball/green.png");
	background-size: 1.1rem
}

.round-4.transparent {
	background: 0 0;
	box-shadow: none;
	color: #fff;
	font-weight: 700;
	width: .9rem;
	height: .9rem
}

.round-4.transparent::before {
	font-weight: 700;
	font-size: 1.1rem
}


.round-6 {
	width: 1rem;
    height: 1rem;
    color: #000;
    text-align: center;
    display: inline-block;
    margin-top: .2rem;
    margin-right: .2rem;
    line-height: 1rem;
    background-size: 1rem;
    color: transparent;
}
.round-6.data-1 {
    background-image: url("../images/ball/1.png");
}
.round-6.data-2 {
    background-image: url("../images/ball/2.png");
}
.round-6.data-3 {
    background-image: url("../images/ball/3.png");
}
.round-6.data-4 {
    background-image: url("../images/ball/4.png");
}
.round-6.data-5 {
    background-image: url("../images/ball/5.png");
}
.round-6.data-6 {
    background-image: url("../images/ball/6.png");
}
.round-6.data-7 {
    background-image: url("../images/ball/7.png");
}
.round-6.data-8 {
    background-image: url("../images/ball/8.png");
}
.round-6.data-9 {
    background-image: url("../images/ball/9.png");
}
.round-6.data-10 {
    background-image: url("../images/ball/10.png");
}

.round-7 {
	width: 1.4rem;
	height: 1.4rem;
	display: inline-block;
	line-height: 1.4rem;
	text-shadow: none;
	text-align: center;
	background-size: 1.4rem;
	color: transparent!important
}

.round-7.data-1 {
	background-image: url("../images/ball/1.png")
}

.round-7.data-2 {
	background-image: url("../images/ball/2.png")
}

.round-7.data-3 {
	background-image: url("../images/ball/3.png")
}

.round-7.data-4 {
	background-image: url("../images/ball/4.png")
}

.round-7.data-5 {
	background-image: url("../images/ball/5.png")
}

.round-7.data-6 {
	background-image: url("../images/ball/6.png")
}

.round-7.data-7 {
	background-image: url("../images/ball/7.png")
}

.round-7.data-8 {
	background-image: url("../images/ball/8.png")
}

.round-7.data-9 {
	background-image: url("../images/ball/9.png")
}

.round-7.data-10 {
	background-image: url("../images/ball/10.png")
}

.round-7.radiusball {
	border-radius: 1.4rem
}

.round-8 {
	width: .95rem;
	height: .95rem;
	display: inline-block;
	line-height: .95rem;
	text-shadow: none;
	text-align: center;
	margin-right: .1rem
}

.round-8.data-1 {
	background-image: url(../images/ball/1.png)
}

.round-8.data-1,.round-8.data-2 {
	background-size: .95rem;
	color: transparent!important
}

.round-8.data-2 {
	background-image: url(../images/ball/2.png)
}

.round-8.data-3 {
	background-image: url(../images/ball/3.png)
}

.round-8.data-3,.round-8.data-4 {
	background-size: .95rem;
	color: transparent!important
}

.round-8.data-4 {
	background-image: url(../images/ball/4.png)
}

.round-8.data-5 {
	background-image: url(../images/ball/5.png)
}

.round-8.data-5,.round-8.data-6 {
	background-size: .95rem;
	color: transparent!important
}

.round-8.data-6 {
	background-image: url(../images/ball/6.png)
}

.round-8.data-7 {
	background-image: url(../images/ball/7.png)
}

.round-8.data-7,.round-8.data-8 {
	background-size: .95rem;
	color: transparent!important
}

.round-8.data-8 {
	background-image: url(../images/ball/8.png)
}

.round-8.data-9 {
	background-image: url(../images/ball/9.png)
}

.round-8.data-10,.round-8.data-9 {
	background-size: .95rem;
	color: transparent!important
}

.round-8.data-10 {
	background-image: url(../images/ball/10.png)
}

.round-9 {
	width: .9rem;
	height: .9rem;
	text-align: center;
	display: inline-block;
	margin-top: .2rem;
	margin-right: .1rem;
	line-height: 1rem;
	border-radius: 1.2rem;
	background-repeat: no-repeat;
	background-position: center center;
	background-size: .9rem;
	color: transparent
}

.round-9.data-1 {
	background-image: url("../images/ball/1.png")
}

.round-9.data-2 {
	background-image: url("../images/ball/2.png")
}

.round-9.data-3 {
	background-image: url("../images/ball/3.png")
}

.round-9.data-4 {
	background-image: url("../images/ball/4.png")
}

.round-9.data-5 {
	background-image: url("../images/ball/5.png")
}

.round-9.data-6 {
	background-image: url("../images/ball/6.png")
}

.round-9.data-7 {
	background-image: url("../images/ball/7.png")
}

.round-9.data-8 {
	background-image: url("../images/ball/8.png")
}

.round-9.data-9 {
	background-image: url("../images/ball/9.png")
}

.round-9.data-10 {
	background-image: url("../images/ball/10.png")
}

.nums-wrap .round-10 {margin-right: 5px}

.round-10 {
	width: 1rem;
    height: 1rem;
    color: #000;
    text-align: center;
    display: inline-block;
    line-height: 1rem;
    background-size: 1rem;
    color: transparent!important;
    border-radius: .2rem;
}

.round-10.data-1 {
	background-image: url("../images/ball/s_1.png")
}

.round-10.data-2 {
	background-image: url("../images/ball/s_2.png")
}

.round-10.data-3 {
	background-image: url("../images/ball/s_3.png")
}

.round-10.data-4 {
	background-image: url("../images/ball/s_4.png")
}

.round-10.data-5 {
	background-image: url("../images/ball/s_5.png")
}

.round-10.data-6 {
	background-image: url("../images/ball/s_6.png")
}

.round-11 {
    width: 1.4rem;
    height: 1.4rem;
    color: #000;
    text-align: center;
    display: inline-block;
    line-height: 1.4rem;
    background-size: 1.4rem;
    color: transparent!important;
    border-radius: .2rem;
}

.round-11.data-1 {
	background-image: url("../images/ball/s_1.png")
}

.round-11.data-2 {
	background-image: url("../images/ball/s_2.png")
}

.round-11.data-3 {
	background-image: url("../images/ball/s_3.png")
}

.round-11.data-4 {
	background-image: url("../images/ball/s_4.png")
}

.round-11.data-5 {
	background-image: url("../images/ball/s_5.png")
}

.round-11.data-6 {
	background-image: url("../images/ball/s_6.png")
}
@charset "UTF-8";

body {
	color: #3c3c3c;
	font-family: "微软雅黑","Microsoft YaHei",Tahoma,HelveticaNeue-Light,"Helvetica Neue Light","Helvetica Neue",Helvetica,Arial,sans-serif;
}

.t-red {
	color: red
}

.t-blue {
	color: #00f
}

.t-green {
	color: green
}

.t-white {
	color: white;
}

.t-black {
	color: black;
}

.pdr-15 {
    padding-right: 15px;
}

.pdt-10 {
    padding-top: 10px;
}

.pdt-15 {
    padding-top: 15px;
}

.mr-5 {
	margin-right: 5px!important;
}

.ml-5 {
	margin-left: 5px!important;
}

.mt-5 {
	margin-top: 5px!important;
}

.bar.bar-positive {
	background: rgba(255,255,255,.02)!important;
}

.bar-header {
	height: 45px;
	color: #fff;
    font-size: 1rem;
    box-shadow: 0 0 5px -3px rgba(0,0,0,.8);
}

.bar-header.bar-light {
    border-bottom: 1px solid rgba(255,255,255,.6);
    background: rgba(255,255,255,.2);
}

.bar-header.bar-energized {
    background: -moz-linear-gradient(top,#feb53b,#fa9309);
    background: -webkit-linear-gradient(top,#feb53b,#fa9309);
    background: linear-gradient(top,#feb53b,#fa9309);
}

.tabs-light>.tabs, .tabs.tabs-light {
	background-color: rgba(255,255,255,.4);
}

.bar-header .title {
    color: #242a32;
    font-weight: 600;
    font-size: 20px;
    margin: 0;
}

.empty-tip {padding-top: 30px; text-align: center;}

.text-light {
	color: #000;
	opacity: .5
}

.text-assertive {
	color: #e00013;
	opacity: .5
}

.list input::-webkit-input-placeholder,.list textarea::-webkit-input-placeholder {
	color: #000;
	opacity: .8
}

.list input:-moz-placeholder,.list textarea:-moz-placeholder,.list input::-moz-placeholder,.list textarea::-moz-placeholder {
	color: #000;
	opacity: .8
}

.list .item {
	background: rgba(255,255,255,.4);
	border-color: #f5f5f5;
	color: #242a32;
	font-size: 14px;
	padding: 7px 10px 6px
}

.list .item .icon {
	margin-right: 10px;
	font-size: 25px;
	opacity: .8
}

.list .item-icon-right {
	padding-right: 28px
}

.list .item-icon-right .icon {
	right: 0;
	color: #fff
}

.list .item-warn {
	margin: 2px 10px;
	font-size: 12px
}

.list .item-note {
	color: #242a32
}

.list .item-select {
	padding-left: 10px
}

.list .item-select img {
	width: 25px
}

.ion-datetime-picker .item-select select {position: inherit; padding-left: 5px!important;}

.list .item-select select {
	position: inherit;
	background: 0 0;
	direction: ltr;
	padding: 0;
	height: 34px
}

.list .item-select:after {
	color: #000;
	opacity: .5
}

.list-inset {
	background: 0 0;
	margin: 10px
}

.list-inset input::-webkit-input-placeholder,.list-inset textarea::-webkit-input-placeholder {
	color: #807b7b;
	opacity: .8
}

.list-inset input:-moz-placeholder,.list-inset textarea:-moz-placeholder,.list-inset input::-moz-placeholder,.list-inset textarea::-moz-placeholder {
	color: #807b7b;
	opacity: .8
}

.input-placeholder input::-webkit-input-placeholder,.list-inset textarea::-webkit-input-placeholder {
	color: #454040;
	opacity: .8
}
.input-placeholder input:-moz-placeholder,.list-inset textarea:-moz-placeholder,.list-inset input::-moz-placeholder,.list-inset textarea::-moz-placeholder {
	color: #454040;
	opacity: .8
}

.list-inset .item {
	padding: 2px 0 2px 8px;
	margin-bottom: 16px
}

.list-inset .item,.list-inset .item input {
	color: #000;
	opacity: .8
}

.list-inset .item select {
	color: #000;
	opacity: .8;
	width: 100%;
	max-width: 80%
}

.list-inset .item textarea {
	color: #000;
	opacity: .8;
	width: 100%;
	background: 0 0
}

.list-inset .item-select,.list-inset .item-input,.list-inset .item-textarea {
	border-radius: 2px;
	border-color: rgba(0,0,0,.35);
	background: rgba(0,0,0,.1)
}

.list-inset .item-input-span {
	height: 34px;
	line-height: 34px;
	display: block;
	width: 90%
}

.list-inset .item-checkbox {
	background: 0 0;
	border: none;
	padding: 2px 0 2px 25px
}

.list-inset .item-checkbox .checkbox {
	padding: 0;
	left: 0;
	width: 28px;
	margin-top: -10px
}

.list-inset .item-checkbox .checkbox input {
	height: 20px;
	width: 20px
}

.list-inset .item-checkbox .checkbox input:before {
	border-radius: 2px;
	border-color: rgba(0,0,0,.35);
	background: rgba(0,0,0,.1);
	border-width: 1px
}

.list-inset .item-checkbox .checkbox input:after {
	top: 25%;
	left: 18%;
	border-width: 2px
}

.list-inset .item-checkbox a {
	text-decoration: underline;
	color: #0077FD
}

.list-inset .item-warn {
	margin: -8px 0 8px
}
.list-inset .item-warn2 {
	margin: 6px 0 8px
}

.list-inset .item-label {
	line-height: 36px
}

.list-inset .item-strength {
	margin: 5px 0 10px
}

.list-inset .item-strength span {
	display: inline-block;
	height: 22px;
	width: 36px;
	text-align: center;
	margin-right: 8px;
	color: #fff
}

.list-inset .item-strength .s-default {
	background: rgba(0,0,0,.4)
}

.list-inset .item-strength .s-weak {
	background: #ff8673
}

.list-inset .item-strength .s-middle {
	background: #ff5a40
}

.list-inset .item-strength .s-strong {
	background: #ff2300
}

.list-inset .row {
	padding: 0;
	margin-bottom: 16px
}

.list-inset .row .col {
	padding: 0 5px
}

.list-inset .row .col:first-child {
	padding-left: 0
}

.list-inset .row .col:last-child {
	padding-right: 0
}

.list-inset .button-block {
	margin-top: 25px
}

.button.button-light {
	border-color: rgba(0,0,0,.35);
	background-color: rgba(255,255,255,.3);
	color: #666
}

.button.button-positive {
	border-width: 0;
	background: -moz-linear-gradient(top,#298dff,#1081ff);
	background: -webkit-linear-gradient(top,#298dff,#1081ff);
	background: linear-gradient(top,#298dff,#1081ff)
}

.tabs-striped {
	position: relative;
	height: 40px
}

.tabs-striped .tabs {
	top: 0;
	height: 40px;
	line-height: 40px;
	border-color: #fff;
	background: rgba(255,255,255,.2)
}

.tabs-striped .tabs .tab-item.active {
	border-color: #e00013;
	color: #e00013
}

.tabs-striped .tabs .tab-item.activated {
	border-color: #e00013
}

.tabs-striped .tabs .tab-item:visited {
	color: inherit
}

.tabs-striped .tabs .tab-item-33 {
	-webkit-box-flex: 0;
	-webkit-flex: 0 0 33.3333%;
	-moz-box-flex: 0;
	-moz-flex: 0 0 33.3333%;
	-ms-flex: 0 0 33.3333%;
	flex: 0 0 33.3333%;
	max-width: 33.3333%;
	width: 33.33%
}

.tab-label {
	height: 100%
}

.tab-label .tab-nav {
	height: 42px;
	background: rgba(255,255,255,.4);
	border-bottom: 1px solid #fff;
	list-style: none
}

.tab-label .tab-nav li {
	position: relative;
	display: block;
	float: left;
	padding: 0;
	text-align: center
}

.tab-label .tab-nav li.active {
	border-bottom: 2px solid #e00013
}

.tab-label .tab-nav li.active a {
	color: #e00013
}

.tab-label .tab-nav li a {
	color: #47535b;
	font-weight: 600;
	display: block;
	padding: 10px 15px;
	text-decoration: none
}

.tab-label .tab-content {
	height: 100%
}

.tab-label .tab-content .tab-pane {
	display: none;
	height: 100%
}

.tab-label .tab-content .active {
	display: block
}

.tab-bable {
	padding: 15px;
	height: 100%
}

.tab-bable .tab-nav {
	margin-bottom: 10px;
	height: 42px;
	list-style: none
}

.tab-bable .tab-nav li {
	position: relative;
	display: block;
	float: left;
	text-align: center;
	background: rgba(255,255,255,.5);
	border-top: 1px solid #665e53;
	border-bottom: 1px solid #665e53;
	border-right: 1px solid #665e53
}

.tab-bable .tab-nav li:first-child {
	border-left: 1px solid #665e53;
	border-bottom-left-radius: 6px;
	border-top-left-radius: 6px
}

.tab-bable .tab-nav li:last-child {
	border-bottom-right-radius: 6px;
	border-top-right-radius: 6px
}

.tab-bable .tab-nav li.active {
	background: rgba(255,255,255,.8)
}

.tab-bable .tab-nav li.active a {
	color: #e00013
}

.tab-bable .tab-nav li a {
	color: #47535b;
	display: block;
	padding: 2px;
	text-decoration: none
}

.tab-bable .tab-content {
	height: 100%
}

.tab-bable .tab-content .tab-pane {
	display: none;
	height: 100%
}

.tab-bable .tab-content .active {
	display: block
}

.tab-bable .tab-content .round {
	border-radius: 2px;
	border: 1px solid rgba(255,255,255,.4);
	background-color: rgba(255,255,255,.08);
	box-shadow: 0 4px 4px 0 rgba(0,0,0,.3);
	padding: 10px;
	margin-bottom: 20px
}

.tab-bable .tab-content input::-webkit-input-placeholder,.tab-bable .tab-content textarea::-webkit-input-placeholder {
	color: #c5c0c0;
	opacity: .8
}

.tab-bable .tab-content input:-moz-placeholder,.tab-bable .tab-content textarea:-moz-placeholder,.tab-bable .tab-content input::-moz-placeholder,.tab-bable .tab-content textarea::-moz-placeholder {
	color: #000;
	opacity: .8
}

.tab-bable .tab-content .item {
	color: #000;
	opacity: .8;
	padding: 2px 0 2px 8px;
	margin-bottom: 16px;
	font-size: 14px
}

.tab-bable .tab-content .item:last-child {
	margin-bottom: -1px
}

.tab-bable .tab-content .item input {
	color: #000
}

.tab-bable .tab-content .item select {
	color: #000;
	opacity: .8;
	width: 100%;
	max-width: 90%
}

.tab-bable .tab-content .item-select,.tab-bable .tab-content .item-input {
	border-radius: 2px;
	border-color: rgba(0,0,0,.35);
	background: rgba(0,0,0,.1)
}

.tab-bable .tab-content .item-select select {
	position: inherit;
	background: 0 0;
	direction: ltr;
	padding: 0;
	height: 34px
}

.tab-bable .tab-content .item-select:after {
	color: #000;
	opacity: .5
}

.tab-bable .tab-content .item-warn {
	margin: -8px 0 8px;
	font-size: 12px
}

.tab-bable .tab-content .row {
	padding: 0;
	margin-bottom: 16px
}

.tab-bable .tab-content .row .col {
	padding: 0 5px
}

.tab-bable .tab-content .row .col:first-child {
	padding-left: 0
}

.tab-bable .tab-content .row .col:last-child {
	padding-right: 0
}

.table-list {
	text-align: center
}

.table-list .table-head {
	background: rgba(255,255,255,.4);
	padding: 0 0 0 10px;
	height: 35px;
	line-height: 35px;
	border-bottom: 1px solid rgba(255,255,255,.7)
}

.table-list .table-head .col {
	line-height: 24px;
	padding: 5px 0
}

.table-list .table-body {
	top: 35px;
	margin: 0;
	color: #4d555f
}

.table-list .table-body .row {
	padding: 0 0 0 10px;
	border-bottom: 1px solid rgba(255,255,255,.7)
}

.table-list .table-body .row+.row {
	margin-top: 0
}

.table-list .table-body .row:nth-child(odd) {
	background: rgba(255,255,255,.5)
}

.table-list .table-body .row:nth-child(even) {
	background: rgba(255,255,255,.4)
}

.table-list .table-body .col {
	padding: 5px 0;
	display: flex;
	-webkit-align-items: center;
	-moz-align-items: center;
	align-items: center;
	-webkit-justify-content: center;
	-moz-justify-content: center;
	justify-content: center;
	word-wrap: break-word;
	word-break: break-word;
	text-align: center;
	flex-direction: column
}

.table-list .table-body p {
	margin: 0
}

.table-list .table-body-nav {
	top: 75px
}

.table-list .table-footer {
	height: 54px
}

.table-list .table-footer .button {
	line-height: 30px;
	min-height: 30px;
	font-weight: 700
}

.table-list .table-footer .button-clear {
	color: #4d555f
}

.table-list .table-footer .col {
	padding: 0
}

.table-list .arrow {
	width: 1px;
	height: 100%;
	position: absolute;
	top: 0;
	right: 0;
	display: block;
	background-image: -ms-linear-gradient(to bottom,rgba(255,255,255,.7)18px,transparent 18px,transparent 31px,rgba(255,255,255,.7)31px);
	background-image: -moz-linear-gradient(to bottom,rgba(255,255,255,.7)18px,transparent 18px,transparent 31px,rgba(255,255,255,.7)31px);
	background-image: -webkit-linear-gradient(to bottom,rgba(255,255,255,.7)18px,transparent 18px,transparent 31px,rgba(255,255,255,.7)31px);
	background-image: -o-linear-gradient(to bottom,rgba(255,255,255,.7)18px,transparent 18px,transparent 31px,rgba(255,255,255,.7)31px);
	background-image: linear-gradient(to bottom,rgba(255,255,255,.7)18px,transparent 18px,transparent 31px,rgba(255,255,255,.7)31px)
}

.table-list .arrow::after {
	content: '';
	display: block;
	position: absolute;
	top: 50%;
	margin-top: -5px;
	left: 0;
	margin-left: -4px;
	width: 9px;
	height: 9px;
	border-top: 1px solid rgba(255,255,255,.7);
	border-right: 1px solid rgba(255,255,255,.7);
	-ms-transform: rotate(45deg);
	-moz-transform: rotate(45deg);
	-webkit-transform: rotate(45deg);
	-o-transform: rotate(45deg);
	transform: rotate(45deg)
}

.table-list .line {
	width: 1px;
	height: 100%;
	position: absolute;
	top: 0;
	right: 0;
	display: block;
	background-color: rgba(255,255,255,.7)
}

.copyright {
	text-align: center;
	margin: 10px 0 20px 0
}

.copyright p {
	color: #000;
	opacity: .5
}

.copyright a {
	padding-left: 25px;
	height: 30px;
	font-size: 16px;
	line-height: 30px;
	display: inline-block;
	color: #FFF
}

.copyright .computer {
	background: url("../images/icon-computer.png") center left no-repeat;
	background-size: 22px
}

.copyright .chrome {
	background: url("../images/icon-chrome.png") center left no-repeat;
	background-size: 22px
}

.popover {
	background: none!important;
	box-shadow: none!important;
	margin-top: 0;
	width: 100px
}

.popover .scroll-content {
	margin: 0!important
}

.popover .popover-arrow {
	display: none
}

.popover .list {
	border-radius: 6px;
	border: 1px solid rgba(0,0,0,.1);
	padding: 0
}

.popover .list .item {
	margin: 0;
	border-bottom: 1px solid rgba(0,0,0,.3);
	background: rgba(255,255,255,.9);
	text-align: center
}

/* .popover .list .item:first-child {
	border-top-left-radius: 6px;
	border-top-right-radius: 6px
}

.popover .list .item:last-child {
	border-bottom-left-radius: 6px;
	border-bottom-right-radius: 6px
} */

.popup-container .popup {
	border-radius: 6px;
}

.popup-head {
	border-bottom: none
}

.popup-title {
	font-size: 18px;
	color: #2bb01f
}

.popup-body {
	overflow: auto
}

.popup-buttons .button {
	border-radius: 6px;
	height: 40px;
	min-height: 40px;
	border-color: #b2b2b2
}

.reset-field-icon {
	margin-right: 5px;
	font-size: 22px;
	opacity: .8;
	padding: 5px 15px
}

.platform-android .popover {
	margin-top: 0;
	background-color: #4d4d4d
}

.platform-android .popover .item {
	border-color: #e6e6e6;
	background-color: #fafafa;
	color: #4d4d4d
}

.country-choose .popup {
	padding-top: 0
}

.country-choose .popup .popup-head {
	display: none
}

.country-choose .popup .popup-body {
	padding: 0
}

.country-choose .popup .popup-body .list {
	border-top-left-radius: 6px;
	border-top-right-radius: 6px
}

.country-choose .popup .popup-body .list .item {
	padding: 0;
	border-color: #e6e6e6;
	background-color: whitesmoke
}

.country-choose .popup .popup-body .list .item .button {
	min-height: 45px;
	text-align: left;
	border: 0;
	line-height: 45px;
	background: #fff;
	padding: 0 20px;
	color: #5c5c5c
}

.country-choose .popup .popup-body .list .item .selected {
	color: #1884FF;
	background-color: #fff
}

.country-choose .popup .popup-body .list .item .button-full {
	margin: 0
}

.country-choose .popup .popup-body .list .item .col-30 {
	background: #fff
}

.country-choose .popup .popup-body .list .item .button-border-right {
	border-right: solid 1px #E0E0E0;
	background: #fff;
	min-height: 25px;
	line-height: 25px;
	margin: 10px 0
}

.country-choose .popup .popup-body .list .item .row,.country-choose .popup .popup-body .list .item .row .col {
	padding: 0
}

.country-choose .popup .popup-body .list .item .row .col input {
	height: 45px;
	line-height: 45px;
	color: #1884FF;
	font-weight: 700;
	font-size: 20px;
	background-color: #fff;
	padding: 0 20px
}

.country-choose .popup .popup-body .list .item:first-child {
	border-top-left-radius: 6px;
	border-top-right-radius: 6px
}

.country-choose .popup .popup-body .list .selected {
	color: #1884FF;
	background-color: #fff
}

.country-choose .popup .popup-buttons {
	padding: 0
}

.country-choose .popup .popup-buttons .button {
	min-height: 45px;
	border: 0;
	font-weight: 700;
	line-height: 45px;
	color: #3c3c3c;
	font-size: 20px;
	background: #efefef
}

.country-choose .popup .popup-buttons .button-full {
	margin: 0
}

.country-choose .item-content {
	padding: 5px
}

.country-choose .item-radio .radio-icon {
	padding: 0
}

.country-choose .item-input {
	border: solid 1px #5b5747
}

.tabs-top.tabs-striped .tabs-theme .tab-item span {
	color: #000
}

/* .tabs-top.tabs-striped .tabs-theme .tab-item.active {
	border-width: 0!important
} */

.tabs-top.tabs-striped .tabs-theme .tab-item.active span {
	display: inline-block;
	color: #df0013!important;
	width: 80px;
	height: 39px;
	border-bottom: 2px solid #ef6b73;
	opacity: 1
}

.tabs-top.tabs-striped .tabs-theme .tab-item.activated {
	border-width: 0!important
}

.tabs-top.tabs-striped .tabs-theme .tab-item.activated span {
	display: inline-block;
	color: #df0013!important;
	width: 80px;
	height: 39px;
	border-bottom: 2px solid #ef6b73;
	opacity: 1
}

.popup-body .item-input-theme input::-webkit-input-placeholder,.popup-body .item-input-theme textarea::-webkit-input-placeholder {
	color: #d3d6db
}

.popup-body .item-input-theme input:-moz-placeholder,.popup-body .item-input-theme textarea:-moz-placeholder,.popup-body .item-input-theme input::-moz-placeholder,.popup-body .item-input-theme textarea::-moz-placeholder {
	color: #d3d6db
}

.button:after {
	bottom: 0
}

.rp-float {
	position: absolute;
	right: 26px;
	top: 231px;
	width: 65px;
	height: 85px;
	background: url("../images/packet-icon.png") center no-repeat;
	background-size: 100%
}

.rp-float a {
	padding: 3px 8px;
	position: absolute;
	top: -20px;
	right: -26px;
	color: #be0000;
	font-size: 16px
}

.rp-state {
	top: -20px;
	width: 100%;
	text-align: center;
	color: #e40001;
	font-size: 16px;
	text-shadow: -1px 0 #fff,0 1px #fff,1px 0 #fff,0 -1px 0 #fff
}

.rp-state,.rp-count {
	position: absolute;
	font-weight: 700
}

.rp-count {
	top: -10px;
	right: 63px;
	color: #ffea00;
	border-radius: 2px;
	font-size: 12px;
	border: 1px solid #ffea00;
	background-color: #e00;
	padding: 0 3px
}

.rp-detail {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 45px;
	overflow: scroll;
	background-color: rgba(0,0,0,.2);
	z-index: 10
}

.rp-content {
	margin: 30px auto 50px;
	width: 260px;
	height: 464px;
	background: url("../images/packet-bg.png") center 49px no-repeat;
	background-size: 100%;
	position: relative
}

.rp-content h3 {
	color: #e4cca1;
	text-align: center;
	margin: -16px auto 10px;
	width: 100px;
	background-color: #d74f43
}

.rp-content .ion-close-round {
	font-size: 24px;
	color: #d74f43;
	position: absolute;
	top: 45px;
	right: -32px;
	padding: 0 4px
}

.rp-content .line {
	clear: both;
	width: 100%;
	margin-top: 10px;
	border: 1px dashed #c4483c
}

.rp-list .rp-info {
	margin: 0 auto 20px;
	padding-top: 4px;
	width: 210px;
	height: 180px;
	background: url("../images/packet-info.png") center no-repeat;
	background-size: 100%;
	text-align: center
}

.rp-list .rp-info p {
	padding-left: 30px;
	font-size: 16px;
	color: #454545;
	text-decoration: underline;
	text-align: left;
	line-height: 46px;
	margin: 0
}

.rp-list .rp-info p span {
	color: #bb4036;
	width: 80px;
	font-weight: 700;
	text-decoration: none;
	display: inline-block
}

.rp-list .rp-info a {
	margin-top: 6px;
	display: inline-block;
	background-color: #d74f43;
	padding: 2px 6px;
	color: #fff;
	border-radius: 2px
}

.rp-list .switch {
	margin-right: 10px;
	float: right;
	line-height: 30px;
	font-size: 16px;
	text-decoration: underline;
	color: #830c01
}

.rp-list .intro {
	height: 194px;
	overflow: scroll;
	padding: 0 13px
}

.rp-list .intro h5 {
	color: #e4cca1;
	font-size: 16px;
	font-weight: 700;
	margin: 0 0 5px
}

.rp-list .intro p {
	color: #ffdfdf
}

.rp-list .rank {
	padding: 0 20px;
	text-align: center
}

.rp-list .rank-head {
	color: #e4cca1;
	font-size: 16px;
	padding-bottom: 0
}

.rp-list .rank-head .col {
	padding-bottom: 0
}

.rp-list .rank-list {
	height: 164px;
	overflow: scroll;
	color: #ffdfdf
}

.rp-list .rank-list .row {
	border-top: 1px dashed #c4483c
}

.rp-list .rank-list .row:first-child {
	border-top: 0
}

.rp-list .rank .current {
	color: #e4cca1
}

.rp-win {
	background-position: center -2px
}

.rp-win .rp-info {
	margin: 0 auto 35px;
	padding-top: 4px;
	width: 210px;
	height: 129px;
	background: url("../images/packet-win.png") center no-repeat;
	background-size: 100%;
	text-align: center
}

.rp-win .rp-info h1 {
	margin-top: 20px;
	color: #bb4036;
	font-size: 40px;
	font-weight: 700
}

.rp-win .rp-info h1 span {
	font-size: 22px
}

.rp-win .rp-info a {
	font-size: 16px;
	margin-top: 15px;
	display: inline-block;
	color: #bb4036;
	text-decoration: underline
}

.rp-win .rule {
	color: #ffdfdf;
	padding: 10px 15px
}

.rp-open {
	margin: 40px auto 50px;
	width: 260px;
	height: 357px;
	background: url("../images/packet-open.png") center no-repeat;
	background-size: 100%;
	position: relative;
	text-align: center
}

.rp-open .ion-close-round {
	font-size: 24px;
	color: #d74f43;
	position: absolute;
	top: -9px;
	right: -32px;
	padding: 0 4px
}

.rp-open .btn-open {
	background: url("../images/packet-kai.png") center no-repeat;
	background-size: 100%;
	display: inline-block;
	padding: 44px;
	margin-top: 60px
}

.rp-open .active {
	animation: rpRotate 1s infinite;
	-webkit-animation: rpRotate 1s infinite
}

.rp-open h3 {
	width: 94%;
	margin: 20px auto 12px;
	color: #e4cca1;
	font-weight: 700;
	font-size: 20px;
	overflow: hidden;
	white-space: nowrap
}

.rp-open p {
	padding: 0 10px;
	color: #ffdfdf
}

.rp-out {
	background-position: center 10px;
	height: 425px;
	padding: 155px 10px 0;
	text-align: center;
	position: relative
}

.rp-out h2 {
	color: #e4cca1;
	font-weight: 700
}

.rp-out p {
	color: #ffdfdf
}

.rp-out .link {
	position: absolute;
	right: 10px;
	bottom: 10px;
	line-height: 30px;
	color: #dbbe89;
	font-size: 16px;
	text-decoration: underline
}

.rp-fail {
	background-position: center 10px;
	height: 425px;
	padding: 165px 10px 0;
	text-align: center;
	position: relative
}

.rp-fail h2 {
	color: #e4cca1;
	font-weight: 700
}

.rp-fail .btn-again {
	display: inline-block;
	width: 200px;
	height: 41px;
	line-height: 41px;
	color: #454545;
	font-weight: 700;
	font-size: 20px;
	background: url("../images/packet-btn.jpg") center no-repeat;
	background-size: 100%
}

@-webkit-keyframes rpRotate {
	from {
		transform: rotateY(0deg)
	}

	to {
		transform: rotateY(360deg)
	}
}

@keyframes rpRotate {
	from {
		transform: rotateY(0deg)
	}

	to {
		transform: rotateY(360deg)
	}
}

.m-index .slider {
	background: rgba(255,255,255,.5);
	min-height: 140px;
}

.m-index .slider-empty {
	min-height: 140px;
	text-align: center;
	padding: 10px
}

.m-index .slider-pager {
	bottom: 15px;
	right: 10px;
	text-align: right
}

.m-index .slider-pager .slider-pager-page {
	margin: 0 1px;
	opacity: 1;
	color: #fff
}

.m-index .slider-pager .slider-pager-page.active {
	color: #fff000
}

.bar-header .logo {
	display: inline-block;
	margin-top: -2px
}

.bar-header .logo img {
	display: block;
	height: 40px
}

.bar-header .slogan {
	margin-left: 5px;
	margin-top: 10px;
	height: 24px;
	vertical-align: super
}

.bar-header .notify {
	display: inline-block!important;
	margin-left: 0!important;
	margin-bottom: 10px;
	border-radius: 5px;
	width: 10px;
	height: 10px;
	background: #e00013
}

.bar-header .langue img {
	width: 30px
}

.scroll-view {
	position: relative;
    display: block;
    overflow: hidden;
    margin-top: -1px;
}

.scroll-view img {
	max-width: 640px;
	width: 100%;
	display: block;
	margin: 0 auto
}

.m-index .login-info {font-size: 16px; line-height: 35px}

.m-index .notice {
	padding: 0;
	height: 35px;
	line-height: 35px;
	background: rgba(255,255,255,.5);
	color: #000;
	border-bottom: 1px solid #fff
}

.m-index .notice .notice-icon {
	padding: 0;
	text-align: center;
	font-size: 24px;
	background: url("../images/icon-notice.png") center no-repeat;
	background-size: 20px
}

.m-index .notice .notice-content {
	padding: 0 8px 0 0
}

.notice .notice-content a {
	color: #000
}

.gamelist .row .col {
	padding: 5px 0
}

.gamelist .title {
	position: relative;
	font-size: 16px;
	font-weight: 700;
	padding: 10px 10px 0 38px
}

.gamelist .title img {
	width: 21px;
	position: absolute;
	left: 10px;
	top: 8px
}

.gamelist .gamebox {
	text-align: center;
	font-size: 16px;
	padding: 10px 4px;
	border: 1px solid #fff;
	-moz-border-radius: 10px;
	-webkit-border-radius: 10px;
	border-radius: 10px;
	margin: 0 5px;
	background: rgba(255,255,255,.5);
	position: relative
}

.gamelist .gamebox .new {
	position: absolute; top: -1px; right: -1px
}

.gamelist .gamebox a {
	width: 100%;
	font-weight: 600;
	color: #575e68;
	text-decoration: none;
	display: block
}

.gamelist .gamebox a img {
	display: block;
	height: 2rem;
	margin: 0 auto
}

.gamelist .gamebox a p {
	margin: 10px 0 0
}

.gamelist .gamebox .tapes {
	position: absolute;
	top: 7px;
	left: -23px;
	display: block;
	width: 100px;
	height: 30px;
	line-height: 30px;
	background: url("../images/g-tapes.png") center no-repeat;
	background-size: 100px;
	transform: rotate(-41deg);
	-ms-transform: rotate(-42deg);
	-moz-transform: rotate(-42deg);
	-webkit-transform: rotate(-41deg);
	-o-transform: rotate(-42deg);
	color: #e68e26;
	font-size: 14px
}

.gamelist .please-wait {
	-webkit-filter: grayscale(100%);
	-moz-filter: grayscale(100%);
	-ms-filter: grayscale(100%);
	-o-filter: grayscale(100%);
	filter: grayscale(100%);
	filter: gray;
	opacity: .4
}

.activity .head {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -moz-flex;
	display: -ms-flexbox;
	display: flex;
	justify-content: space-between;
	padding: 10px;
	font-size: 16px
}

.activity .head .title {
	position: relative;
	font-weight: 700;
	padding-left: 28px
}

.activity .head .title img {
	width: 21px;
	position: absolute;
	left: 0;
	top: -1px
}

.activity .head a {
	color: #3c3c3c
}

.activity-list {
	margin: 0 10px;
	padding: 10px;
	border: 1px solid #fff;
	border-radius: 10px;
	background: rgba(255,255,255,.5)
}

.activity-list a {
	color: #3c3c3c
}

.activity-list p {
	font-size: 16px;
	font-weight: 700
}

.activity-list img {
	width: 100%
}

.has-footer {
    bottom: 55px;
}

ion-footer-bar.max {height: 55px}

.bar-footer {
	padding: 0;
	background: rgba(255,255,255,.4);
	border-top: 1px solid #fff;
	position: absolute;
	display: block;
	bottom: 0;
}

.bar-footer .tabs {
	height: 100%;
	border: none;
	background: rgba(255,255,255,.4);
}

.bar-footer .tabs img {
	width: 30px;
	display: block;
	margin: 0 auto
}

.bar-footer .tab-item {
	opacity: 1;
	line-height: 25px!important;
	color: #000
}

.bar-footer .tab-item span {
	opacity: .6
}

.add-home {
	position: absolute;
	bottom: 10px;
	left: 0;
	right: 0;
	z-index: 10
}

.add-home-content {
	border: solid 1px #000;
	border-radius: 6px;
	height: 70px;
	padding-left: 70px;
	padding-top: 5px;
	background: rgba(255,254,241,.9);
	max-width: 200px;
	position: relative;
	margin: 0 auto
}

.add-home img {
	height: 50px;
	position: absolute;
	top: 50%;
	margin-top: -25px;
	left: 10px
}

.add-home p {
	margin: 5px 0 0
}

.add-home em {
	position: absolute;
	bottom: -20px;
	left: 50%;
	margin-left: -10px;
	width: 0;
	height: 0;
	border: 10px solid #000;
	border-color: #000 transparent transparent;
	z-index: 5
}

.add-home em:before {
	content: "";
	position: absolute;
	bottom: -8px;
	right: -9px;
	width: 0;
	height: 0;
	border: 9px solid rgba(255,254,241,.9);
	border-color: #fffef1 transparent transparent;
	z-index: 3
}

.add-home .ion-ios-upload-outline {
	font-size: 22px;
	margin-left: 5px;
	color: #007aff
}

.add-home .ion-close-round {
	position: absolute;
	top: 0;
	right: 0;
	display: block;
	width: 25px;
	height: 25px;
	line-height: 25px;
	text-align: center;
	color: rgba(0,0,0,.7)
}

.m-center .top-info {
	background: rgba(255,255,255,.1);
	padding: 20px 0;
}

.m-center .top-info .col {
	padding: 0
}

.m-center .top-info h5 {
	color: #f0ecec;
	font-size: 16px;
	font-weight: 600;
	margin-top: -5px;
	margin-bottom: 8px
}

.m-center .top-info p {
	margin-bottom: 6px;
	color: #f0ecec
}

.m-center .top-info .avatar {
	width: 76px;
	display: block;
	margin: 0 auto
}

.m-center .top-info .money {
	opacity: 1;
	color: #f15e22;
	font-weight: 700;
	margin-right: 3px
}

.m-center .top-info .refresh img {
	width: 20px;
	margin-bottom: -4px
}

.m-center .top-info .level {
	margin-bottom: 0;
	line-height: 1rem
}

.m-center .top-info .level span {
	position: relative;
	padding-left: .9rem;
	margin-right: 5px;
	background-repeat: no-repeat;
	background-size: auto 1rem;
	display: inline-block;
	height: 1rem
}

.m-center .top-info .level span:nth-child(1) {
	background-image: url("../images/l-ag.png")
}

.m-center .top-info .level span:nth-child(2) {
	background-image: url("../images/l-egame.png")
}

.m-center .top-info .level span:nth-child(3) {
	background-image: url("../images/l-lottery.png")
}

.m-center .top-info .level span:nth-child(4) {
	background-image: url("../images/l-maya.png")
}

.m-center .top-info .level img {
	width: 1rem
}

.m-center .top-info .test-goto-reg {
	background-color: #fff;
	color: #20a9e9;
	font-size: 12px;
	margin: 0 0 0 10px;
	font-weight: 400;
	padding: 3px 5px;
	display: inline-block;
	position: relative;
	top: -2px;
	-moz-border-radius: .3em;
	border-radius: .3em
}

.m-center .top-testuser-info h5 {
	margin-top: 10px
}

.m-center .center-list .row {
	padding: 0;
	border-top: 1px solid rgba(255,255,255,.7);
	background: rgba(255,255,255,.5);
	height: 6rem
}

.m-center .center-list .row+.row {
	margin-top: 0
}

.m-center .center-list .row:last-child {
	border-bottom: 1px solid rgba(255,255,255,.7)
}

.m-center .center-list .col {
	position: relative;
	padding: 0;
	border-left: 1px solid rgba(255,255,255,.7)
}

.m-center .center-list .col:first-child {
	border-left: none
}

.m-center .center-list a {
	width: 100%;
	display: block;
	text-align: center;
	border-radius: 0;
	border: none;
	text-decoration: none;
	color: #47535b;
	top: 50%;
	margin-top: -1.9rem;
	position: absolute
}

.m-center .center-list a img {
	width: 3rem
}

.m-center .center-list a p {
	margin: 0
}

.m-center .center-list .notify {
	display: inline-block;
	border-radius: 10px;
	width: 20px;
	height: 20px;
	background: #e00013;
	position: absolute;
	color: #fff
}

.m-center .center-list .test-convert {
	position: absolute;
	z-index: 1;
	background-color: #20a9e9;
	width: 100%;
	height: 100%;
	opacity: .9;
	text-align: center
}

.m-center .center-list .test-convert p {
	font-size: 16px;
	font-weight: 700;
	line-height: 25px;
	color: #fff;
	margin-top: .8rem;
	width: 100%
}

.m-center .center-list .test-convert a {
	top: 0;
	margin-top: 10px;
	background-color: #fff;
	color: #20a9e9;
	font-size: 16px;
	font-weight: 700;
	position: static;
	padding: 5px 10px;
	display: inline;
	-webkit-border-radius: .3em;
	-moz-border-radius: .3em;
	border-radius: .3em
}

.m-info .content {
	border-bottom: 1px solid #fff;
	background-color: rgba(255,255,255,.36);
	margin-bottom: 15px;
	padding: 10px 10px 5px
}

.m-info .content .level {
	margin-bottom: 0;
	line-height: 1rem
}

.m-info .content .level span {
	position: relative;
	padding-left: .9rem;
	margin-right: 5px;
	background-repeat: no-repeat;
	background-size: auto 1rem;
	display: inline-block;
	height: 1rem
}

.m-info .content .level span:nth-child(1) {
	background-image: url("../images/l-ag.png")
}

.m-info .content .level span:nth-child(2) {
	background-image: url("../images/l-egame.png")
}

.m-info .content .level span:nth-child(3) {
	background-image: url("../images/l-lottery.png")
}

.m-info .content .level span:nth-child(4) {
	background-image: url("../images/l-maya.png")
}

.m-info .content .level img {
	width: 1rem
}

.m-info .list .item {
	line-height: 30px;
	margin-top: 1px;
	margin-bottom: 0
}

.m-info .list .item-icon-right {
	padding-right: 30px
}

.m-info .list .item-icon-right .icon {
	color: #000;
	opacity: .4;
	font-size: 20px
}

.m-info .data-theme .content {
	background: #fff;
	padding: 10px 15px;
	border-bottom: 1px solid #d3d6db
}

.m-info .data-theme .content p {
	color: #242a32;
	font-size: 110%
}

.m-info .data-theme .list .nobtm {
	border-bottom: 0
}

.m-info .data-theme .list .item {
	margin-top: 0;
	line-height: 40px;
	padding-left: 15px;
	padding-right: 15px
}

.m-info .data-theme .list .item span {
	font-size: 110%
}

.m-info .data-theme .list .item span.item-note-edit {
	color: #2189ff
}

.m-info .data-theme .list .item .icon {
	margin-right: 10px;
	border-bottom: 2px solid #eee;
	height: 20px;
	margin-top: 15px;
	width: 20px;
	color: #b2b2b2;
	opacity: 1
}

.m-info .data-theme .list .item-icon-right {
	padding-right: 35px
}

.info-mdf .popup .popup-buttons {
	background: #fff;
	border-top: 0!important
}

.info-mdf .popup .popup-buttons button.button-positive {
	margin-right: 0
}

.info-mdf .popup .popup-buttons button.button-positive b {
	color: #fff
}

.info-mdf .popup .popup-buttons button.button-positive:first-child {
	background: 0 0;
	border: 1px solid #9f9f9f;
	margin-right: 5%!important
}

.info-mdf .popup .popup-buttons button.button-positive:first-child b {
	color: #666
}

.info-mdf .popup .popup-body {
	background: #fff
}

.list-mytheme {
	font-size: 16px
}

.list-mytheme .item-note-edit {
	color: #2189ff;
	text-decoration: underline
}

.list-mytheme .item span,.content-mytheme p {
	font-size: 16px
}

.m-message .message-list {
	top: 45px
}

.m-message .message-list .list .item {
	padding: 10px 20px 5px 20;
	position: relative;
	overflow: inherit;
	background: 0 0;
	border-radius: 0;
	border: none;
	border-bottom: 1px solid rgba(0,0,0,.2)
}

.m-message .message-list .list .item h3 {
	line-height: 25px;
	color: #000;
	opacity: .8;
	overflow: hidden
}

.m-message .message-list .list .item p {
	color: #000;
	opacity: .5
}

.m-message .message-list .list .item p .icon {
	font-size: 20px;
	vertical-align: middle
}

.m-message .message-list .list .item p img {
	width: 18px;
	height: 18px;
	vertical-align: middle
}

.m-message .message-list .list .item .ion-record {
	position: absolute;
	top: 12px;
	left: -20px;
	margin: 0;
	font-size: 12px;
	color: #2c8fff
}

.m-message .message-list .list .item .reply {
	background-color: rgba(0,0,0,.2);
	color: #fff;
	padding: 2px 3px;
	border-radius: 2px
}

.m-message .test-user-message {
	top: 5px
}

.m-message .InternalMessage-list {
	top: 44px
}

.m-message .InternalMessage-list .list {
	padding-top: 0
}

.m-message .InternalMessage-list .list .item {
	margin-left: 35px;
	padding: 10px 20px 5px 0;
	position: relative;
	overflow: inherit;
	background: 0 0;
	border-radius: 0;
	border: none;
	border-bottom: 1px solid rgba(0,0,0,.2)
}

.m-message .InternalMessage-list .list h3 {
	overflow: hidden;
	line-height: 25px;
	color: #000;
	opacity: .8
}

.m-message .InternalMessage-list .list p {
	color: #000;
	opacity: .5
}

.m-message .InternalMessage-list .list p .icon {
	font-size: 20px;
	vertical-align: middle
}

.m-message .InternalMessage-list .list p img {
	width: 18px;
	height: 18px;
	vertical-align: middle
}

.m-message .InternalMessage-list .list .item-content {
	background-color: rgba(255,255,255,0);
	padding: 0;
	overflow: visible
}

.m-message .InternalMessage-list .list .ion-record {
	position: absolute;
	top: 4px;
	left: -20px;
	margin: 0;
	font-size: 12px;
	color: #2c8fff
}

.m-message .InternalMessage-list .list .reply {
	background-color: rgba(0,0,0,.2);
	color: #fff;
	padding: 2px 3px;
	border-radius: 2px
}

.m-message .list-date {
	margin: 10px 0 0;
	padding-left: 20px;
	padding-right: 0
}

.m-message .list-date .item {
	background: rgba(255,255,255,0);
	border: none;
	padding-left: 0;
	padding-right: 0;
	border-bottom: 1px solid rgba(0,0,0,.2);
	vertical-align: bottom
}

.m-message .list-content {
	margin: 10px 0 0;
	padding-left: 20px;
	padding-right: 20px
}

.m-message .list-content .item {
	padding-left: 0;
	padding-right: 0;
	background: rgba(255,255,255,0);
	border: none
}

.m-message .list-content .item p {
	color: #000;
	opacity: .8
}

.m-message .list-reply {
	border-radius: 4px;
	border: 1px solid rgba(255,255,255,.5);
	margin: 8px 20px 0;
	padding: 10px;
	background: rgba(204,230,242,.8);
	color: rgba(0,0,0,.6);
	box-shadow: 0 2px 6px 0 rgba(0,0,0,.3)
}

.m-message .list-reply .content {
	margin-top: 0
}

.m-message .list-reply .content p {
	margin: 10px 0 0
}

.m-message .list-reply .content .reply-time {
	opacity: .5
}

.m-message .list-reply .transform {
	border-radius: 4px;
	border: 1px solid rgba(255,255,255,.5);
	border-bottom: none;
	border-right: none;
	background: #C7E1EC;
	margin-top: -18px;
	margin-left: 10px;
	margin-bottom: 10px;
	width: 15px;
	height: 15px;
	-ms-transform: rotate(45deg);
	-moz-transform: rotate(45deg);
	-webkit-transform: rotate(45deg);
	-o-transform: rotate(45deg);
	transform: rotate(45deg)
}

.m-message .button-padding {
	padding-left: 10px;
	padding-right: 10px
}

.m-cash .cash-top {
	background: rgba(255,255,255,.4)
}

.m-cash .cash-top .sub-header {
	border-bottom: 1px solid #fff;
	padding: 12px 5px;
	color: #64717a
}

.m-cash .cash-top .sub-header .col {
	padding: 0
}

.m-cash .cash-top .sub-header h5 {
	color: #47535b;
	font-size: 16px;
	font-weight: 600;
	margin-bottom: 8px
}

.m-cash .cash-top .sub-header p {
	margin-bottom: 6px;
	color: #64717a
}

.m-cash .cash-top .sub-header .avatar {
	width: 76px;
	display: block;
	margin: 0 auto
}

.m-cash .cash-top .sub-header .money {
	opacity: 1;
	color: #f15e22;
	font-weight: 700;
	margin-right: 3px
}

.m-cash .cash-top .sub-header .refresh img {
	width: 20px;
	margin-bottom: -4px
}

.m-cash .transfer {
	margin-top: 15px
}

.trans-detail {padding: 0 10px; height: 300px}

.m-cash .transfer .sub-title {
	text-align: left;
	background: rgba(255,255,255,.3);
	padding: 10px 16px;
	font-size: 16px
}

.m-cash .transfer .sub-title:first-child {
	border-top: 1px solid #fff
}

.m-cash .transfer .sub-title .refresh img {
	width: 22px
}

.m-cash .transfer .row {
	display: block;
	padding: 0;
	border-bottom: 1px solid #fff
}

.m-cash .transfer .row .col {
	display: inline-block;
	text-align: center;
	background-color: rgba(255,255,255,.5);
	border-top: 1px solid #fff;
	border-right: 1px solid #fff
}

.m-cash .transfer .row .col p {
	margin: 0
}

.m-cash .transfer .row .col span {
	line-height: 25px;
	font-size: 16px
}

.m-cash .transfer .row .choose {
	background: rgba(255,255,255,.8)url("../images/icon-selected.png") top left no-repeat;
	background-size: 30px
}

.m-cash .transfer .row .choose span {
	color: #e00013
}

.m-cash .transfer .row .unchoose {
	color: gray
}

.m-cash .transfer-submit {
	margin-top: 10px;
	padding: 16px 0
}

.m-cash .transfer-submit h5 {
	font-size: 16px;
	margin: 0 16px 5px;
	opacity: .8
}

.m-cash .transfer-submit .row {
	border: none;
	padding: 3px 10px
}

.m-cash .transfer-submit .row .col {
	padding: 0;
	border: none;
	background: 0 0
}

.m-cash .transfer-submit .row .col span {
	border: 1px solid rgba(255,255,255,.6);
	display: block;
	margin: 5px 6px;
	height: 40px;
	line-height: 40px;
	background: rgba(255,255,255,.3)
}

.m-cash .transfer-submit .row .col input {
	background: 0 0;
	width: 100%;
	padding-left: 10px;
	padding-right: 2px;
	height: 38px;
	text-align: left;
	font-size: 16px
}

.m-cash .transfer-submit .row .choose span {
	background: #32a1ff;
	border-color: #32a1ff;
	color: #fff
}

.m-cash .transfer-submit .item {
	border-color: rgba(255,255,255,.6);
	background: rgba(255,255,255,.3);
	margin: 5px 6px
}

.m-cash .transfer-submit .item-input {
	padding: 0
}

.m-cash .transfer-button {
	padding-right: 16px;
	padding-left: 16px
}

.m-cash .q-transfer h5 {
	padding-left: 10px;
	opacity: .6
}

.m-cash .q-transfer .row {
	display: block
}

.m-cash .q-transfer .row .col {
	display: inline-block;
	padding: 10px
}

.m-cash .q-transfer .item {
	border: 1px solid #fff;
	border-radius: 6px;
	background-color: rgba(255,255,255,.5);
	text-align: center;
	padding: 5px;
	position: relative
}

.m-cash .q-transfer .item img {
	width: 3.5rem
}

.m-cash .q-transfer .item p {
	font-size: 16px;
	line-height: 25px;
	margin: 0
}

.m-cash .q-transfer .item-load {
	border-color: rgba(0,0,0,.5)
}

.m-cash .q-transfer .item-load-icon {
	height: 100%;
	width: 100%;
	position: absolute;
	left: 0;
	top: 0;
	background-color: rgba(0,0,0,.5);
	display: flex;
	align-items: center;
	-webkit-align-items: center;
	justify-content: center;
	-webkit-justify-content: center
}

.m-cash .q-transfer .item-load-icon .spinner {
	position: absolute;
	top: 50%;
	margin-top: -20px;
	right: 50%;
	margin-right: -20px;
	stroke: #FFF
}

.m-cash .q-transfer .item-done {
	position: absolute;
	font-size: 22px;
	right: 5px;
	top: 5px;
	color: #66737c
}

.m-cash .q-transfer .item-error {
	position: absolute;
	font-size: 22px;
	right: 5px;
	top: 5px;
	color: #e00013
}

.m-cash .withdraw .limit {
	padding-top: 30px;
	text-align: center
}

.m-cash .withdraw .limit p {
	font-size: 20px;
	color: #2c8fff
}

.m-cash .withdraw .limit span {
	color: #e00013
}

.m-cash .withdraw-exist {
	text-align: center;
	margin-top: 40px
}

.m-cash .withdraw-exist h3 {
	margin-top: 5px;
	font-size: 20px;
	color: #322d24
}

.m-cash .withdraw-exist p {
	padding: 0 40px;
	margin-top: -20px;
	color: #5b5747
}

.m-cash .withdraw-exist .luka {
	width: 100px
}

.m-cash .withdraw-exist .light {
	width: 300px;
	margin-top: -30px
}

.m-cash .withdraw-success {
	margin: 30px;
	border: 1px solid #FFF;
	border-radius: 4px;
	background: rgba(255,255,255,.5);
	position: relative;
	box-shadow: 0 4px 4px 0 rgba(0,0,0,.3);
	overflow: hidden;
	padding-top: 80px;
	padding-bottom: 20px
}

.m-cash .withdraw-success .top {
	background: -moz-radial-gradient(rgba(255,255,255,.8),rgba(255,255,255,.4),rgba(255,255,255,.1));
	background: -webkit-radial-gradient(rgba(255,255,255,.8),rgba(255,255,255,.4),rgba(255,255,255,.1));
	background: radial-gradient(rgba(255,255,255,.8),rgba(255,255,255,.4),rgba(255,255,255,.1));
	box-shadow: 0 6px 4px -1px rgba(0,0,0,.1);
	border-radius: 50%;
	padding-left: 30%;
	padding-top: 60px;
	position: absolute;
	width: 100%;
	top: -85px
}

.m-cash .withdraw-success .top p:first-child {
	padding-top: 30px
}

.m-cash .withdraw-success .money {
	color: #009b71;
	margin-bottom: 15px;
	font-size: 18px
}

.m-cash .withdraw-success h3 {
	color: #e00013;
	text-align: center
}

.m-cash .withdraw-success p {
	text-align: left;
	line-height: 28px;
	margin: 0 20px
}

.m-cash .trans .tab-content .list {
	clear: both;
	width: 100%;
	margin-bottom: 0
}

.m-cash .trans .tab-content .list li {
	display: block;
	width: 100%;
	padding: 7px 5px;
	margin-bottom: 5px
}

.m-cash .trans .tab-content .list li .trans-date {
	padding: 5px;
	background-color: #fff;
	-moz-border-radius: 10px;
	-webkit-border-radius: 10px;
	border-radius: 10px;
	color: #000;
	opacity: .4
}

.m-cash .trans .tab-content .list li .trans-arrow {
	display: block;
	height: 35px;
	line-height: 35px;
	padding: 0 10px;
	margin-bottom: 10px;
	border-top-right-radius: 6px;
	border-top-left-radius: 6px;
	background: #929283;
	color: #fff
}

.m-cash .trans .tab-content .list li .trans-arrow-less {
	height: 25px;
	line-height: 25px
}

.m-cash .trans .tab-content .list li .trans-info {
	width: 85%;
	background-color: rgba(255,255,255,.8);
	border-radius: 6px;
	font-size: 14px;
	box-shadow: 2px 2px 10px rgba(0,0,0,.3);
	color: #808173
}

.m-cash .trans .tab-content .list li .trans-info .bolder {
	font-weight: 700;
	font-size: 16px;
	color: #333
}

.m-cash .trans .tab-content .list li .trans-info.success .trans-arrow {
	background-color: #1aa465
}

.m-cash .trans .tab-content .list li .trans-info.success .bolder .text-right {
	color: #1aa465
}

.m-cash .trans .tab-content .list li .trans-info.col-offset-15 {
	margin-left: 15%
}

.m-cash .trans .tab-content .list li .trans-info .row {
	padding: 0 10px 10px;
	margin: 0
}

.m-cash .trans .tab-content .list .left {
	text-align: left
}

.m-cash .trans .tab-content .list .right {
	text-align: right
}

.m-cash .m-lock {
	margin-top: 20px;
	text-align: center
}

.m-cash .m-lock h4 {
	color: #e61b2c;
	font-size: 22px;
	font-weight: 700
}

.m-cash .m-lock img {
	width: 5.5rem
}

.m-cash .color-red {
	color: #e00013
}

.m-cash .input-number::-webkit-inner-spin-button {
	-webkit-appearance: none
}

.m-cash .input-number::-webkit-outer-spin-button {
	-webkit-appearance: none
}

.order-detail .popup {
	padding-top: 0
}

.order-detail .popup .popup-head {
	background: #f7f7f7;
	border-bottom: 1px solid #e7e7e7;
	border-top-left-radius: 5px;
	border-top-right-radius: 5px
}

.order-detail .popup .popup-head h3 {
	color: #000
}

.order-detail .popup .popup-head h3 span {
	color: #788097
}

.order-detail .popup-body {
	padding: 10px 20px;
	background: #fff;
	border-bottom-left-radius: 5px;
	border-bottom-right-radius: 5px
}

.order-detail .popup-body .color-red {
	color: #e40000
}

.confirmbox-theme {
	margin-bottom: 30px
}

.confirmbox-theme p {
	margin: 0
}

.confirmbox-theme .confirm-title,.offline-pay-tip {
	background: #ef6c74;
	color: #fffdff;
	/* height: 30px;
	line-height: 30px; */
	padding: 5px
}

.confirmbox-theme .confirm-title span,.offline-pay-tip span{
	display: inline-block;
	background: #fff;
	color: #ef6c74;
	width: 15px;
	height: 15px;
	border-radius: 10px;
	text-align: center;
	font-family: fantasy;
	line-height: 15px
}

.confirmbox-theme .confirm-con {
	border: 1px solid #f5f5f5;
	border-top: 0
}

.confirmbox-theme .confirm-con .confirm-contitle {
	height: 35px;
	line-height: 35px;
	color: #000;
	padding-left: 15px;
	font-size: 15px;
	background: rgba(255,255,255,.3)
}

.confirmbox-theme .confirm-con .confirm-context {
	padding: 10px 15px;
	background: #fff;
	border-top: 1px solid #f5f5f5;
	border-bottom: 1px solid #f5f5f5;
	font-size: 14px;
	background: rgba(255,255,255,.15)
}

.confirmbox-theme .confirm-con .confirm-context p {
	line-height: 25px;
	color: #44525d;
	font-size: 14px
}

.confirmbox-theme .confirm-con .text-remark {
	padding: 5px 15px;
	background: rgba(255,255,255,.3)
}

.confirmbox-theme .confirm-con .text-remark p {
	line-height: 25px;
	color: rgba(0,0,0,.5);
	font-size: 12px
}

.confirmbox-theme .confirm-con .text-remark p:first-child {
	color: #e00013;
	font-weight: 700;
	font-size: 15px
}

.button.button-positive-theme {
	background: #33a1ff;
	font-size: 18px
}

.underlines {
	text-decoration: underline
}

.responsive-sm .responsive-mess {
	background: url("../images/responsive-mess.png") no-repeat center center;
	background-size: 100%;
	width: 80px;
	height: 80px;
	margin-top: 50px
}

.responsive-sm .responsive-recard {
	background: url("../images/responsive-recard.png") no-repeat center center;
	background-size: 100%;
	width: 80px;
	height: 53px;
	margin-top: 50px
}

.responsive-sm .responsive-txt {
	font-size: 18px
}

.responsive-sm .responsive-btn a {
	line-height: 47px
}

.responsive-sm .responsive-btn a,.responsive-sm .responsive-btn a.activated {
	width: 45%;
	margin: 10px auto 0;
	background: #33a1ff
}

@media only screen and (max-width:320px) {
	.confirmbox-theme .confirm-title {
		font-size: 90%
	}
}

.col-65 {
	max-width: 65%
}

.col-35 {
	max-width: 35%
}

.list-new {
	margin-top: 10px
}

.list-new a {
	padding: 10px 10px 10px 0;
	width: 100%;
	display: inline-block;
	background: url("../images/arrow-r.png") no-repeat 95% center;
	background-size: 4%
}

.list-new a img {
	width: 3em;
	float: left;
	margin-right: 10px
}

.list-new a p {
	line-height: 100%;
	display: flex;
	font-size: .85rem;
	color: #3c3c3c
}

.list-new a span {
	display: block;
	color: #5c5c5c;
	font-size: .6rem;
	margin-top: 4px
}

.list-new a .ion-ios-arrow-back {
	transform: rotate(360deg)
}

.list-new .item-input {
	background: rgba(255,255,255,.4);
	border: 1px solid #e1ebef
}

.list-new .item {
	padding: 0 0 0 7%;
	height: 66px
}

.list-new .item:last-child a {
	border-bottom: 0
}

.list-new .noborder {
	border: 0!important
}

.item-online {
	padding: 3%
}

.item-online .item-select {
	height: 47px;
	margin-top: 5px;
	background: rgba(255,255,255,.4);
	border: 1px solid #e1ebef
}

.item-online .item-select select {
	padding: 14px 35px 14px 16px;
	max-width: 100%;
	width: 100%;
	left: 0;
	right: inherit;
	direction: inherit;
	background: 0 0
}

.item-online .item-input {
	background: rgba(255,255,255,.4);
	border: 1px solid #e1ebef
}

.item-online .text-light {
	margin: 5px 0
}

.item-online .red {
	color: #e00013
}

.item-online .blue {
	color: #45b452
}

.item-bank .row {
	padding: 0
}

.item-bank .col {
	padding: 0;
	max-width: 47%;
	margin-right: 6%
}

.item-bank .col:last-child {
	margin-right: 0
}

.item-bank .item-input {
	background: rgba(255,255,255,.4);
	border: 1px solid #e1ebef
}

.item-bank .item-select {
	border: 1px solid #e1ebef;
	background: 0 0
}

.item-bank .item-select select {
	padding: 14px 35px 14px 16px;
	max-width: 100%;
	width: 100%;
	left: 0;
	right: inherit;
	direction: inherit;
	background: rgba(255,255,255,.4)
}

.item-bank .text-light {
	margin: 5px 0
}

.item-bank .item-round {
	padding: 15px;
	font-size: 14px;
	line-height: 24px;
	margin-top: 10px;
	margin-bottom: 10px;
	background: rgba(255,255,255,.4);
	border: 1px solid #e1ebef
}

.itme-whechat .reminder {
	margin-top: 15px
}

.itme-whechat .reminder p {
	margin: 0;
	line-height: 20px
}

.whechat-content {
	text-align: center;
	background: rgba(255,255,255,.4);
	padding-top: 10px
}

.whechat-content p {
	color: #9c9c9c;
	margin: 0;
	line-height: 35px
}

.whechat-content p img {
	width: 50%
}

.whechat-content .wechat-user {
	line-height: 30px;
	font-size: 16px
}

.whechat-content .wechat-user img {
	vertical-align: middle;
	width: .8rem
}

.whechat-content .money {
	font-size: 1rem;
	line-height: 20px;
	color: #45b452
}

.whechat-content .pay {
	color: #e00013;
	line-height: 45px;
	background: rgba(255,255,255,.2)
}

.whechat-content .pay img {
	width: 1rem;
	vertical-align: middle;
	margin-right: 5px
}

.item-alipay .reminder {
	margin-top: 15px
}

.item-alipay .reminder p {
	margin: 0;
	line-height: 20px
}

.item-alipay select {
	padding: 14px 0 16px 10px;
	direction: inherit;
	text-align: left;
	max-width: 100%;
	width: 100%;
	left: 0;
	right: inherit;
	background: rgba(255,255,255,.4)
}

.alipay-content {
	text-align: center;
	background: rgba(255,255,255,.4);
	padding-top: 10px
}

.alipay-content img {
	width: 50%
}

.alipay-content .alipay-user {
	line-height: 30px;
	font-size: 16px
}

.alipay-content .alipay-user img {
	vertical-align: middle;
	width: .8rem
}

.alipay-content p {
	color: #3c3c3c;
	margin: 0;
	line-height: 25px
}

.alipay-content .pd20 {
	padding-left: 25%
}

.alipay-content .money {
	color: #45b452;
	font-size: 1rem;
	line-height: 20px
}

.alipay-content .pay {
	text-align: center;
	margin-top: 15px;
	border-top: 1px solid #f5f5f5;
	color: #e00013;
	line-height: 45px
}

.alipay-content .pay img {
	width: 1rem;
	vertical-align: middle;
	margin-right: 5px
}

.pay-reminder {
	padding: 0;
	color: #46473f
}

.pay-reminder .pay-title {
	line-height: 30px;
	padding-left: 3px;
	border-bottom: 1px solid #bfbeab
}

.pay-reminder .pay-con {
	padding: 3%;
	line-height: 20px
}

.pay-reminder .pay-con img {
	width: 100%
}

.pay-reminder .pay-con p {
	margin: 10px 0;
	color: #414141
}

.m-bank .tips {
	margin-bottom: 0;
	padding: 3px 15px;
	color: red;
	background-color: rgba(255,255,255,.77);
	font-size: 12px
}

.m-bank .button-addbank {
	border: 1px dashed #615d53;
	background: rgba(255,255,255,.2);
	padding: 30px 12px;
	font-size: 20px;
	opacity: .8
}

.m-bank .list-bank .item {
	margin: 0 15px 15px;
	-moz-border-radius: 15px;
	-webkit-border-radius: 15px;
	border-radius: 15px;
	padding: 0;
	white-space: normal
}

.m-bank .list-bank .item .row {
	border-bottom: solid 1px #fff
}

.m-bank .list-bank .item .row .col {
	padding-left: 12px;
	padding-right: 10px
}

.m-bank .list-bank .item .row .col p {
	margin-top: 5px
}

.m-bank .list-bank .item .row:last-child {
	border-bottom: none
}

.m-bank .list-bank .item a {
	padding-top: 8px;
	display: inline-block;
	margin-left: 5px
}

.m-bank .list-bank .item a:first-child {
	margin-left: 0
}

.m-bank .list-bank .item a img {
	width: 25px
}

.m-bank .bank-round img {
	width: 100%
}

.m-bank .bank-round>div {
	padding: 5px
}

.m-bank .bank-border {
	border-bottom: solid 1px #fff
}

.m-bank .list {
	margin-top: 15px;
	margin-bottom: 0
}

.m-bank .list  input::-webkit-input-placeholder {
	color: #8c8787;
	opacity: .8
}

.m-bank .list  input::-moz-placeholder {
	color: #8c8787;
	opacity: .8
}

.m-report .row-data {
	line-height: 40px
}

.m-report .underline {
	text-decoration: underline
}

.m-report .win {
	color: #00b939
}

.m-report .lose {
	color: #e00013
}

.m-report .bg-default {
	color: #fff;
	background-color: #4d555f;
	border-color: #4d555f
}

.m-report .bg-lose {
	color: #fff;
	background-color: #e00013;
	border-color: #e00013
}

.m-report .bg-win {
	color: #fff;
	background-color: #00b939;
	border-color: #00b939
}

.m-report .breadcrumb {
	padding: 10px;
	color: #fff
}

.m-report .breadcrumb .current {
	color: #4d555f
}

.m-report .rpt-nav {
	text-align: left;
	padding: 10px;
	font-size: 14px
}

.m-report .rpt-nav a {
	text-decoration: none;
	color: #fff
}

.m-report .rpt-nav .gray {
	color: #888;
	font-size: 14px
}

.m-report .table-body .foot-pads {
	padding: 5px 0;
	color: #000
}

.m-report .table-body .foot-detext {
	padding-bottom: 5px;
	color: #6a6d72
}

.m-report .table-body .foot-text {
	padding-bottom: 5px;
	color: #979ea7
}

.m-report .table-body .foot-pad {
	padding: 5px 0
}

.m-report .table-sport .scroll .row {
	background: 0 0;
	padding-left: 0
}

.m-report .table-sport .scroll .row .padbtm5 {
	width: 100%;
	padding-bottom: 5px
}

.m-report .table-sport .scroll .row .col-33 {
	min-width: 33%
}

.m-report .table-sport .scroll .row .foot-pads {
	padding: 0 0 0 10px;
	height: 35px;
	line-height: 35px;
	background: rgba(255,255,255,.6);
	color: #47535b
}

.m-report .table-sport .scroll .row .foot-detext {
	padding: 5px 10px;
	line-height: 25px;
	border-top: 1px solid #fff;
	border-bottom: 1px solid #fff;
	background: rgba(255,255,255,.4);
	color: #4d555f
}

.m-report .table-sport .scroll .row .foot-pad {
	padding: 0 0 0 10px;
	height: 35px;
	line-height: 35px;
	background: rgba(255,255,255,.6);
	color: #47535b
}

.m-report .table-sport .scroll .row .foot-text {
	padding: 5px 10px;
	line-height: 25px;
	border-top: 1px solid #fff;
	border-bottom: 1px solid #fff;
	background: rgba(255,255,255,.4);
	color: #4d555f
}

.m-report .table-sport .scroll .row:last-child .padbtm5 {
	padding-bottom: 0
}

.m-report .table-sport .scroll .row:last-child .padbtm5 .foot-text,.m-report .table-sport .scroll .row:last-child .padbtm5 .foot-detext {
	border-bottom: none
}

.m-activity img {
	width: 100%;
	display: block
}

.m-activity .list {
	padding: 10px
}

.m-activity .list a {
	margin-top: 10px;
	display: block;
	color: #3c3c3c;
	border-top-left-radius: 10px;
	border-top-right-radius: 10px;
	border: 1px solid #fff;
	background: rgba(255,255,255,.5)
}

.m-activity .list p {
	font-size: 16px;
	font-weight: 700;
	padding-top: 10px;
	padding-left: 10px
}

.m-activity .detail {
	padding: 10px
}

.m-activity .detail .title {
	margin-top: 15px;
	margin-bottom: 5px;
	font-size: 20px;
	font-weight: 700;
	color: rgba(0,0,0,.8)
}

.m-activity .detail .date {
	font-size: 16px;
	line-height: 30px;
	color: rgba(0,0,0,.5);
	border-bottom: 1px solid rgba(0,0,0,.2)
}

.m-activity .detail h3 {
	font-weight: 700;
	font-size: 18px;
	color: #454741
}

.m-login .list {
	margin-top: 20px;
	margin-bottom: 0
}

.m-login .list .item {
	margin: -1px 0;
	border-radius: 0;
	background: rgba(255,255,255,.6);
	border-color: #fff;
	padding: 12px 10px
}

.m-login .list .item:first-child {
	border-top-left-radius: 4px;
	border-top-right-radius: 4px
}

.m-login .list .item:last-child {
	border-bottom-right-radius: 4px;
	border-bottom-left-radius: 4px
}

.m-login .list .item-checkbox {
	background: 0 0
}

.m-login .list .item-checkbox label {
	padding-left: 15px;
	color: #000;
	opacity: 1;
	font-size: 16px
}

.m-login .list .item-checkbox .checkbox {
	padding: 0
}

.m-login .list .item.item-input img {
	width: 18px;
	margin-right: 5px
}

.m-login .list .item.item-input input {
	margin-left: 10px;
	padding-right: 5px
}

.m-login .list-remember {
	margin-top: 0
}

.valid-img {width: 90px!important; height: 30px}

.text-underline {
	text-decoration: underline!important
}

.m-register .success {
	text-align: center;
	padding-top: 20px;
	padding-bottom: 30px;
	position: relative
}

.m-register .success h3 {
	color: #009B71;
	margin-top: 40px;
	margin-bottom: 5px;
	font-size: 28px;
	font-weight: 700
}

.m-register .success p {
	color: #000;
	font-size: 16px
}

.m-register .success .cheer {
	width: 230px
}

.m-register .success .cheer,.m-register .success .light {
	position: absolute;
	display: block;
	left: 0;
	right: 0;
	margin: 0 auto
}

.m-register .success .light {
	width: 300px;
	bottom: 0
}

.button-bar {
	padding: 10px 0 0
}

.button-bar .button {
	padding: 10px 0 0;
	border: none;
	max-height: none;
	text-align: center;
	position: relative;
	color: #64717a
}

.bar .user-button {
	color: #fff;
	border-color: transparent;
	background: 0 0;
	box-shadow: none;
	font-size: .75rem;
    margin: auto 5px;
}

.bar .span-user {
    background: url("../images/userinfo.png") center left no-repeat;
    padding-left: 1rem;
    background-size: .75rem;
    line-height: 1rem;
}

.bar .topinfo-spinner svg {margin-top: 5px; margin-right: 10px;}

.bar .button.ry-button {
	background-color: rgba(250,252,250,.01);
    padding: 3px 5px;
    border: solid 1px rgba(0,0,0,.1);
    box-shadow: inset 1px 1px 0 rgba(250,250,250,.22);
    color: #fff;
    min-height: 0;
    line-height: 1.1rem;
    font-size: .75rem;
    margin: auto 0;
}

.button-bar .button-clear.active,.button-bar .button-clear.activated {
	opacity: 1
}

.button-bar .num {
	display: block;
	margin: 0 auto;
	background: #fff;
	border: 1px solid #64717a;
	height: 26px;
	line-height: 24px;
	width: 26px;
	font-weight: 700;
	border-radius: 13px
}

.button-bar .line {
	position: absolute;
	top: 21px;
	display: block;
	width: 50%;
	height: 4px;
	background: #64717a
}

.button-bar .line-left {
	left: 0;
	margin-left: -13px
}

.button-bar .line-right {
	right: 0;
	margin-right: -13px
}

.button-bar p {
	line-height: 30px;
	margin: 0
}

.button-bar .current {
	color: #2c8fff
}

.button-bar .current .num {
	background: #2c8fff;
	color: #fff;
	border: 1px solid #2c8fff
}

.button-bar .current .line,.button-bar .current+.button .line-left {
	background: #2c8fff
}

.agreement .scroll-view {
	height: 15rem
}

.fl {
	float: left
}

.fr {
	float: right
}

.po {
	position: absolute
}

.pr {
	position: relative
}

.text-center {
	text-align: center
}

.padding10 {
	padding: 0 10px
}

.padding15 {
	padding: 0 15px
}

.nomargin {
	margin-right: 0!important
}

.list-theme {
	padding: 0 15px
}

.list-theme .item-theme-title {
	color: #7c838c;
	margin-top: 10px
}

.list-theme .border-theme {
	border: 1px solid #fff
}

.list-theme .item-input-theme {
	width: 48%;
	margin-right: 2%;
	display: inline-block
}

.list-theme .item-input-theme .item-input-span i {
	right: 8px!important
}

.list-theme .col-theme .label {
	height: 45px
}

.list-theme .item-select {
	border: 1px solid #fff;
	height: 45px;
	background: url("../images/inputbg.png")
}

.list-theme .item-select select {
	height: 43px;
	line-height: 45px
}

.list-theme .item-input {
	border: 1px solid #fff;
	background: url("../images/inputbg.png");
	height: 40px
}

.list-theme .item-input span {
	height: 43px;
	line-height: 45px
}

.list-theme .input-images {
	background: url("../images/inputbgx.png") repeat-y;
	background-size: 100%
}

.list-theme .input-images input {
	padding-left: 15%;
	height: 45px
}

.list-theme .input-name input {
	background: url("../images/icon-user.png") no-repeat 8px center;
	background-size: 22px;
	opacity: 1
}

.list-theme .input-pwd input {
	background: url("../images/icon-pwd.png") no-repeat 8px center;
	background-size: 22px;
	opacity: 1
}

.list-theme .input-email input {
	background: url("../images/icon-email.png") no-repeat 8px center;
	background-size: 26px;
	opacity: 1
}

.list-theme .next-btn {
	background: #32a1ff;
	font-size: 20px
}

.list-theme ::-webkit-input-placeholder {
	color: #d3d6db;
	font-weight: 400
}

.list-theme ::-moz-placeholder {
	color: #d3d6db;
	font-weight: 400
}

.list-theme :-ms-input-placeholder {
	color: #d3d6db;
	font-weight: 400
}

.list-theme input:-moz-placeholder {
	color: #d3d6db;
	font-weight: 400
}

.list-theme .item-checkbox {
	margin-left: 0
}

.list-theme .item-checkbox .checkbox-theme {
	padding: 0;
	left: 0;
	width: 28px;
	margin-top: -10px
}

.list-theme .item-checkbox .checkbox-theme input {
	height: 20px;
	width: 20px
}

.list-theme .item-checkbox .checkbox-theme input:before {
	border-radius: 2px;
	border-color: #fff;
	background: #d5d8da;
	border-width: 1px
}

.list-theme .item-checkbox .checkbox-theme input:after {
	top: 25%;
	left: 18%;
	border-width: 2px
}

.list-theme .item-checkbox label {
	padding-left: 25px;
	color: #000
}

.list-theme .item-checkbox a {
	color: #000;
	text-decoration: none;
	font-size: 14px
}

.list-theme .button-block {
	margin: 25px 0;
	width: 100%;
	background: #1d87ff;
	font-size: 18px
}

.list-theme .col-theme p {
	color: #fff;
	font-size: 16px
}

.list-theme .col-theme p a {
	margin-left: 30px;
	color: #fff;
	text-decoration: underline
}

.list-theme .select-item .text-label {
	background: url(../images/inputbgx.png) repeat-y; border-right: 1px #fff solid; padding: 0 5px; display: block;
}

.list-theme .select-item .sel-wrap {
	margin-bottom: 0px; height: 100%
}

.list-theme .select-item .sel-wrap select {
	border: none; 
	border-radius: 0; 
	appearance:none; 
	-moz-appearance:none; 
	-webkit-appearance:none;
	padding: 0 15px!important;
	background: none;
	font-size: 18px;
	font-weight: bold;
}

.info-mdf .popup {
	padding-top: 0;
	width: 90%;
	margin: 0 5%;
	border-radius: 8px
}

.info-mdf .popup .popup-head {
	border-bottom: 1px solid #ccc
}

.info-mdf .popup .popup-buttons {
	border-top: 1px solid #ccc;
	border-bottom-left-radius: 8px;
	border-bottom-right-radius: 8px;
	padding-bottom: 20px;
	min-height: 50px
}

.info-mdf .popup .popup-buttons button {
	height: 50px;
	min-height: 50px
}

.info-mdf .popup .popup-buttons button b {
	color: #32a1ff;
	font-family: "HelveticaNeue-Light"
}

.info-mdf .popup .popup-buttons button:first-child {
	margin-right: 0;
	border-right: 1px solid #ccc;
	border-bottom-left-radius: 8px;
	margin-right: 5%!important
}

.info-mdf .popup .popup-buttons button:last-child {
	border-bottom-right-radius: 8px
}

.info-mdf .popup .info-mdf-tip a {
	color: red;
	text-decoration: underline;
	margin-left: 10px
}

.info-mdf .popup .row {
	padding: 5px 0
}

.info-mdf .popup .row .item-input-theme {
	height: 47px
}

.info-mdf .popup .row .item-input-theme span {
	color: #000
}

.info-mdf .popup .row .item-input-theme span i {
	position: absolute;
	right: 10%;
	color: #ccc
}

.info-mdf .popup .row .col-30 {
	padding-left: 0
}

.info-mdf .popup .row .col-70 {
	padding-right: 0
}

.info-mdf .popup .row .item {
	border-color: #e0e0e0;
	border-radius: 3px
}

.info-mdf .popup .row .item ::-webkit-input-placeholder {
	color: #ccc;
	font-weight: 400
}

.info-mdf .popup .row .item ::-moz-placeholder {
	color: #ccc;
	font-weight: 400
}

.info-mdf .popup .row .item :-ms-input-placeholder {
	color: #ccc;
	font-weight: 400
}

.info-mdf .popup .row .item input:-moz-placeholder {
	color: #ccc;
	font-weight: 400
}

.info-mdf .popup .item-input-row {
	width: 100%
}

.list-theme .item {
	padding: 0!important
}

.input-pwd .icon {
	margin-right: 10px
}

.input-pwd .icon-eye {
	background: url("../images/icon-eye.png") no-repeat center center;
	background-size: 100%;
	width: 25px;
	height: 25px
}

.input-pwd .icon-eyes {
	background: url("../images/icon-eyes.png") no-repeat center center;
	background-size: 100%;
	width: 25px;
	height: 25px
}

.popup-body .row .item-input-row .icon {
	margin-right: 10px
}

.m-fgpwd .list {
	margin-top: 15px
}

.m-fgpwd .checkcode {
	height: 38px
}

.mdf-info .list {
	margin-top: 30px
}

.mdf-info .list .item .icon {
	opacity: .5
}

.info-mdf .popup .popup-head h3.popup-title {
	color: #000!important
}

.info-mdf .popup .popup-head h3.popup-title span {
	color: #788097
}

.info-mdf .popup .item-input-row input {
	padding-right: 0
}

.m-mdfpwd .list {
	margin-top: 30px
}

.cur-action-icon.active {
	background-color: rgba(0,0,0,.6)
}

.cur-action-icon .action-sheet-wrapper {
	max-width: 100%
}

.cur-action-icon .action-sheet {
	margin: 0;
	background: #fff
}

.cur-action-icon .action-sheet-title {
	color: #2bb01f;
	border-bottom: 1px solid #dfdfdf;
	font-size: 16px;
	padding: 10px
}

.cur-action-icon .action-sheet-group {
	margin: 0;
	padding: 15px 10px;
	display: flex
}

.cur-action-icon .action-sheet-group .row {
	padding: 0
}

.cur-action-icon .action-sheet-group .row .col {
	padding: 0;
	text-align: center
}

.cur-action-icon .action-sheet-group .row .col img {
	display: block;
	width: 50px;
	margin: 0 auto 3px
}

.cur-action-icon .action-sheet-cancel {
	padding: 10px 20px
}

html {
	font-size: 20px
}

@media only screen and (min-width:320px) {
	.m-index .slider,.m-index .slider-empty {
		min-height: 140px
	}
}

@media only screen and (min-width:360px) {
	html {
		font-size: 22px!important
	}

	.m-index .slider,.m-index .slider-empty {
		min-height: 157px
	}
}

@media only screen and (min-width:401px) {
	html {
		font-size: 24px!important
	}

	.m-index .slider,.m-index .slider-empty {
		min-height: 175px
	}
}

@media only screen and (min-width:428px) {
	html {
		font-size: 26px!important
	}
}

@media only screen and (min-width:481px) {
	html {
		font-size: 28px!important
	}
}

@media only screen and (min-width:569px) {
	html {
		font-size: 30px!important
	}
}

@media only screen and (min-width:641px) {
	html {
		font-size: 32px!important
	}

	.m-center .top-info {
		height: 146px
	}

	.m-center .top-info p {
		margin-bottom: 10px
	}

	.m-center .center-list {
		top: 143px
	}

	.m-report .row-special {
		line-height: 40px
	}
}

@media only screen and (max-height:360px) {
	.agreement .scroll-view {
		height: 150px
	}
}

.m-ext {
	background: url("../images/bg-topic.jpg");
	background-size: cover
}

.m-ext .logo {
	min-height: 78px;
	padding: 5px 0 0
}

.m-ext .logo img {
	height: 85px
}

.m-ext .title {
	padding: 0 10px
}

.m-ext .title img {
	width: 80%
}

.m-ext .p-game {
	position: relative;
	padding: 15px 0;
	margin-bottom: 10px;
	margin-top: -10px
}

.m-ext .p-game .line {
	position: absolute;
	top: 0;
	left: 50%;
	margin-left: -1px;
	height: 100%
}

.m-ext .p-game .row {
	padding: 0;
	margin: 0;
	background-image: linear-gradient(to bottom,rgba(255,255,255,.8),rgba(255,255,255,0))
}

.m-ext .p-game .row:first-child {
	background-image: linear-gradient(to top,rgba(255,255,255,.8),rgba(255,255,255,0))
}

.m-ext .p-game .row:first-child .col {
	border-top: none;
	border-bottom: 1px solid rgba(255,255,255,.4)
}

.m-ext .p-game .row .col {
	display: block;
	padding: 0;
	border-top: 1px solid rgba(0,0,0,.4)
}

.m-ext .p-game .p-box {
	text-align: center;
	font-size: 16px;
	position: relative;
	padding: 20px 5px
}

.m-ext .p-game .p-box a {
	width: 100%;
	text-decoration: none;
	display: block
}

.m-ext .p-game .p-box a img {
	display: block;
	height: 2rem;
	margin: 0 auto
}

.m-ext .p-game .p-box p {
	color: #1c2013;
	margin: 0;
	line-height: normal
}

.m-ext .p-game .p-box .p-title {
	font-size: 1.3rem;
	font-weight: 700;
	font-family: 'fzzzhongjw'
}

.m-ext .p-game .p-box .p-title span {
	color: #4b7627
}

.m-ext .p-game .p-box .p-sub-title {
	font-size: 1rem;
	font-weight: 700
}

.m-ext .p-game .p-box .p-info {
	margin-top: 10px;
	font-size: 12px
}

.m-ext .p-game .p-box .tapes {
	position: absolute;
	top: 20px;
	left: 3px;
	display: block;
	width: 60px;
	height: 20px;
	line-height: 17px;
	border: 2px solid #740800;
	transform: rotate(-41deg);
	-ms-transform: rotate(-42deg);
	-moz-transform: rotate(-42deg);
	-webkit-transform: rotate(-41deg);
	-o-transform: rotate(-42deg);
	color: #740800;
	font-weight: 700;
	font-size: 12px
}

.m-ext .p-game .p-box .f-small {
	font-size: 8px;
	font-weight: 400;
	padding: 5px 0
}

.m-ext .p-game .please-wait p {
	color: #696969
}

.m-ext .btn-try {
	background: url("../images/btn-try.png");
	background-size: 250px;
	border: 0;
	width: 250px;
	height: 55px;
	font-weight: 700;
	font-size: 20px;
	padding-bottom: 10px
}

.m-ext .enter {
	margin-top: 10px;
	display: block;
	margin-left: 50px
}

.m-ext .enter img {
	width: 230px
}

.m-ext .copyright {
	color: rgba(255,255,255,.5);
	margin-top: 20px
}

.m-index ion-slide img {width: 100%}

.spinner-dark {
	position: absolute;
	top: 50%;
	right: 50%;
	margin-top: -13px;
	margin-right: -13px;
	stroke: #FFF
}

.spinner svg {
	width: 26px;
	height: 26px;
	vertical-align: middle
}

.my-popover .spinner svg {   
     width: 18px; height: 18px;
}

.my-popover .popover-arrow:after {
	background-color: #fff
}

.my-popover .popover-conten.scroll-content {
	margin: 0;
	background-color: rgba(255,255,255,.51)
}

.my-popover .list {
	padding: 0;
	text-align: center
}

.my-popover .list .item {
	background-color: #fff;
	padding: 7px;
	font-size: 16px
}

.my-popover .list .color-orange {
	color: #f95c38
}

.my-popover .list .color-blue {
	color: #548ae4
}

.my-popover .list .color-br-gray {
	background-color: #e6e6e6
}

.my-popover .list a {
	font-weight: bolder;
	color: #000
}

.spinner-warp {
	margin-top: 50px;
    text-align: center;
}

.ion-record:before { content: "\e607"; }

.table-list .no-title {
	text-align: center;
	padding: 0;
	height: 1.5rem;
	line-height: 1.5rem;
	font-size: .6rem
}

.table-list .no-title.pt {
	margin-top: .5rem
}

.table-list .no-title .radiu {
	background: rgba(255,255,255,.2);
	padding: 0 0 0 .05rem;
	box-shadow: inset -1px 1px 0 0 rgba(255,255,255,.21),1px 1px 0 0 rgba(0,0,0,.13)
}

.table-list .tips {
	color: #000;
	height: 1.5rem;
	line-height: 1.5rem;
	padding-left: 10px
}

.table-list .tips a {
	color: #fff;
	font-size: .6rem
}

.table-list .list {
	position: absolute;
	margin-bottom: 0;
	top: 1.5rem;
	width: 100%
}

.table-list .row {
	padding: 0;
	font-size: .6rem;
	margin-bottom: 0px;
}

.table-list .list .row .tal {
	box-shadow: inset -1px 1px 0 0 rgba(255,255,255,.2),1px 1px 0 0 rgba(0,0,0,.1);
	margin-bottom: 6px;
	padding: 0 .05rem 0 0;
	background-color: rgba(255,255,255,.05);
	padding: .5rem .25rem
}

.modal .bar-modal {background: rgb(125, 173, 209)}

.pay-success{
	background:#fff;
}

.pay-success .container{
	margin:20% auto 0;
}
.pay-success .icon-pay-success{
	margin: 0 auto;
	width:20%;
}
.pay-success img{
	width:100%;
}
.pay-success .text-pay-success{
	margin: 25px auto;
}


.pay-success .text-pay-success span{
	font-weight:bold;
	padding: 0 3px;
}

.pay-success .text-pay-success p{
	margin:0 20% 5px;
}
.pay-success .text-pay-success p:first-child{
	margin:0 auto 20px;
	text-align:center;
	font-size:1.2em;
}

.pay-success .text-pay-success p:last-child{
	text-align:center;
	margin-top:40px;;
	font-size:1.1em;
}

.pay-success .text-pay-success .pay-logs{
	background:rgb(0,174,240);
	color:#fff;
	text-decoration:none;
	padding:8px 30px;
	border-radius:30px;
}


@font-face {font-family: "iconfont";
  src: url('./fonts/iconfont.eot?t=1474386297'); /* IE9*/
  src: url('./fonts/iconfont.eot?t=1474386297#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('./fonts/iconfont.woff?t=1474386297') format('woff'), /* chrome, firefox */
  url('./fonts/iconfont.ttf?t=1474386297') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
  url('./fonts/iconfont.svg?t=1474386297#iconfont') format('svg'); /* iOS 4.1- */
}

.button-icon {padding-left: 0!important;}
.icon:before, .button-icon:before {
  display: inline-block;
  font-family: "iconfont";
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  text-rendering: auto;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-home:before { content: "\e600"; }
.icon-mima:before { content: "\e601"; }
.icon-yonghuicon:before { content: "\e602"; }
.icon-01:before { content: "\e605"; }
.icon-12:before { content: "\e606"; }
.icon-29:before { content: "\e608"; }
.icon-close:before { content: "\e603"; }
.icon-youxi:before { content: "\e61a"; }
.icon-user:before { content: "\e60a"; }
.icon-user2:before { content: "\e60b"; }
.icon-money:before { content: "\e60c"; }
.icon-dengyu:before { content: "\e60e"; }
.icon-prepage:before { content: "\e60d"; }
.icon-record:before { content: "\e607"; }
.icon-liebiao:before { content: "\e609"; }
.icon-yonghu:before { content: "\e610"; }
.icon-yonghu2:before { content: "\e611"; }
.icon-youjian:before { content: "\e612"; }
.icon-kaisuo:before { content: "\e615"; }
.icon-shouji:before { content: "\e616"; }
.icon-xiugaimima:before { content: "\e617"; }
.icon-zhangdan:before { content: "\e618"; }
.icon-jiahao:before { content: "\e614"; }
.icon-homehover:before { content: "\e604"; }

@charset "UTF-8";


.left {
    float: left;
}

.right {
    float: right;
}

.platform-android .popover {
	margin-top: 10px;
	background-color: #4d4d4d
}

.platform-android .popover .item {
	border-color: #e6e6e6;
	background-color: #fafafa;
	color: #4d4d4d
}

.platform-android .popover-arrow {
	position: absolute;
	display: block;
	top: -17px;
	width: 10px;
	height: 19px;
	overflow: hidden;
	border-left: 15px solid transparent;
	border-right: 15px solid transparent;
	border-bottom: 8px solid #fff
}

.platform-android .popover-arrow:after {
	position: absolute;
	top: 12px;
	left: 10px;
	width: 10px;
	height: 20px;
	border-radius: 3px;
	content: ''
}

.platform-android .popover-bottom .popover-arrow {
	top: auto;
	bottom: -10px
}

.platform-android .popover-bottom .popover-arrow:after {
	top: -6px
}

/* select {
	background-color: rgba(214,207,225,.13);
	margin: 10px
}

select,.pd5 {
	padding: 5px
} */

.notice-icon {
	padding: 0 .5rem;
	font-size: 1.3rem
}

.notice-content {
	padding: 0 .4rem 0 0;
	font-size: .8rem
}

.popup-title {
	font-size: .75rem;
	font-weight: bolder;
	color: #000;
}

.lh-23px {
	line-height: 23px!important
}

.lh-40px {
	line-height: 40px
}

.lh-4 {
	line-height: 4!important
}

.lh-3 {
	line-height: 3!important
}

.lh-1 {
	line-height: 1!important
}

.text-l {
	text-align: left!important
}

.h-20 {
	height: 20px
}

.h-15 {
	height: 15px
}

.pdl-1 {
	padding-left: 1px
}

.pdl-4 {
	padding-left: 4px
}

.pdl-5 {
	padding-left: 5px
}

.pdl-10 {
	padding-left: 10px
}

.pdl-15 {
	padding-left: 15px
}

.pdr-15 {
	padding-right: 15px
}

.pdt-8 {
	padding-top: 8px
}

.pdt-5 {
	padding-top: 5px
}

.background1::before {
	background: url("../images/guangDongShiYiXuanWu.png")
}

.background2::before {
	background: url("../images/gdKuaiLeShiFen.png")
}

.background3::before {
	background: url("../images/chongQingShiShiCai.png")
}

.background4::before {
	background: url("../images/beiJingSaiChe.png")
}

.background5::before {
	background: url("../images/xingYunNongChang.png")
}

.background6::before {
	background: url("../images/xingYunFeiTing.png")
}

.background7::before {
	background: url("../images/shengDiCai.png")
}

.background8::before {
	background: url("../images/jiangXiShiShiCai.png")
}

.background9::before {
	background: url("../images/guangDongShiYiXuanWu.png")
}

.background10::before {
	background: url("../images/bjpk10.png")
}

.my-select {
	width: 8.5rem
}

.my-select select {
	width: 7.5rem;
	border: 1px solid rgba(250,250,250,.44);
	border-radius: .15rem;
	background-color: rgba(0,0,0,.2);
	position: inherit;
	direction: ltr;
	padding: 0;
	color: #fff;
	height: 1.4rem;
	font-size: .6rem;
	line-height: .7rem;
	text-indent: .3em
}

.my-select:after {
	position: absolute;
	color: #fff;
	right: 3rem;
	top: 50%;
	border-top: .4rem solid;
	border-right: .25rem solid transparent;
	border-left: .25rem solid transparent;
	padding: 0;
	margin: -.16rem 0 0
}

.row-width {
	width: 0
}

.col-31 {
	-webkit-flex: 0 0 31%;
	-moz-flex: 0 0 31%;
	-ms-flex: 0 0 31%;
	flex: 0 0 31%;
	max-width: 31%
}

.col-31,.col-23 {
	-webkit-box-flex: 0;
	-moz-box-flex: 0
}

.col-23 {
	-webkit-flex: 0 0 23%;
	-moz-flex: 0 0 23%;
	-ms-flex: 0 0 23%;
	flex: 0 0 23%;
	max-width: 23%
}

.col-40 {
	-webkit-flex: 0 0 40%;
	-moz-flex: 0 0 40%;
	-ms-flex: 0 0 40%;
	flex: 0 0 40%;
	max-width: 40%
}

.col-40,.col-45 {
	-webkit-box-flex: 0;
	-moz-box-flex: 0
}

.col-45 {
	-webkit-flex: 0 0 45%;
	-moz-flex: 0 0 45%;
	-ms-flex: 0 0 45%;
	flex: 0 0 45%;
	max-width: 45%
}

.text-valign-center {
	display: flex;
	align-items: center
}

.text-align-center {
	display: flex;
	justify-content: center
}

.pd0 {
	padding: 0
}

.mt0 {
	margin-top: 0
}

.ml5 {
	margin-left: 5px
}

.ml10 {
	margin-left: 10px
}

.mr10 {
	margin-right: 10px
}

.tp40 {
	top: 30px
}

.right30 {
	right: 1.5rem
}

.text-underline {
	text-decoration: underline
}

.cur-tips {
	font-size: .7rem
}

.resultData {
	border: solid 1px rgba(255,255,255,.29);
	background: rgba(255,255,255,.19);
	border-radius: 4px;
	text-decoration: none;
	color: #fff;
	text-align: center;
	margin-right: .1rem;
	font-size: .6rem;
	width: 1.2rem;
	height: 1rem;
	line-height: .9rem;
	display: inline-block;
	margin-top: .1rem;
	margin-bottom: .2rem
}

.resultData-width {
	width: 1.4rem
}

.h-1-4rem {
	height: 1.4rem
}

.h-1-1rem {
	height: 1.1rem
}

.index .lottery-top {
	margin-top: 45px
}

.index .lottery-list {
	top: 4.1rem
}

.index .lottery-list .row {
	height: 100%;
	display: block;
	padding: 0 .4rem 10px;
	clear: both
}

.index .lottery-list .row .item {
	float: left;
	border: none;
	background: 0 0;
	text-align: center;
	padding: 10px;
	width: 50%
}

.index .lottery-list .row .item a {
	position: relative;
	display: block;
	width: 100%;
	border: solid 1px rgba(255,255,255,.29);
	background: rgba(255,255,255,.19);
	box-shadow: 0 5px 4px -1px rgba(0,0,0,.1);
	border-radius: 6px;
	text-decoration: none;
	color: #fff;
	text-align: center;
	padding: 25px 5px
}

.index .lottery-list .row .item a::before {
	display: block;
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	border-radius: 2px;
	box-shadow: inset 0 0 10px -3px rgba(255,255,255,.4);
	background-size: 100% 100%
}

.index .lottery-list .row .item a h3 {
	color: #fff;
	font-weight: 700;
	text-align: center;
	font-size: .8rem;
	margin-bottom: 25px
}

.index .lottery-list .row .item a span {
	background: rgba(255,255,255,.08);
	padding: 4px 12px;
	border: solid 1px rgba(0,0,0,.1);
	border-radius: 5px;
	position: relative;
	font-weight: 700
}

.index .lottery-list .row .item a span::before {
	display: block;
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	border-radius: 5px;
	box-shadow: inset 0 0 8px -2px rgba(255,255,255,.5)
}

.index .lottery-list .spinner svg {
    width: 16px;
    height: 16px;
}

.index .notice {
	color: rgba(0,0,0,.74);
	padding: 0;
	height: 35px;
	line-height: 35px;
	text-shadow: 0 1px 0 rgba(255,255,255,.23);
	position: relative
}

.index .no-title {
	padding: 10px;
	text-align: center;
	font-size: .7rem;
	line-height: 1rem;
	border-bottom: solid 1px rgba(255,255,255,.26);
	background-color: rgba(0,0,0,.08)
}

.index .no-title .backgrounduser {
	background: url("../images/userinfo.png") center left no-repeat;
	padding-left: 1rem;
	background-size: .8rem;
	line-height: 1rem;
	color: #fff
}

.index .no-title .backgroundmoney {
	background: url("../images/money.png") center left no-repeat;
	padding-left: 1rem;
	background-size: .8rem;
	line-height: 1rem;
	color: #fff
}

.index .no-title span {
	font-size: .7rem
}

.index .no-title i {
	font-size: 1rem
}

.index .no-title .button.ry-button {
	background-color: rgba(250,252,250,.01);
    padding: 0 5px;
    border: solid 1px rgba(0,0,0,.1);
    box-shadow: inset 1px 1px 0 rgba(250,250,250,.22);
    color: #fff;
    min-height: 0;
    height: 1.2rem;
    line-height: 1.1rem;
    font-size: .7rem;
}

.index .no-title .button.ry-button.activated {
	border-color: rgba(0,0,0,.3);
	background-color: rgba(0,0,0,.5);
	color: #fff;
	box-shadow: inset 0 1px 4px rgba(0,0,0,.1),0 0 1px rgba(250,250,250,.3)
}

.icon-size::before {
	font-size: 145%!important
}

.tryPlayIcon {
	padding-top: 4px;
	padding-left: 8px;
	z-index: 1;
	position: relative
}

.rp-float {
	position: absolute;
	right: 26px;
	top: 231px;
	width: 65px;
	height: 85px;
	background: url("../images/packet-icon.png?v=u860dba6e") center no-repeat;
	background-size: 100%;
	z-index: 15
}

.rp-float a {
	padding: 3px 8px;
	position: absolute;
	top: -20px;
	right: -26px;
	color: #be0000;
	font-size: 16px
}

.rp-state {
	top: -20px;
	width: 100%;
	text-align: center;
	color: #e40001;
	font-size: 16px;
	text-shadow: -1px 0 #fff,0 1px #fff,1px 0 #fff,0 -1px 0 #fff
}

.rp-state,.rp-count {
	position: absolute;
	font-weight: 700
}

.rp-count {
	top: -10px;
	right: 63px;
	color: #ffea00;
	border-radius: 2px;
	font-size: 12px;
	border: 1px solid #ffea00;
	background-color: #e00;
	padding: 0 3px
}

.rp-detail {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 45px;
	overflow: scroll;
	background-color: rgba(0,0,0,.2);
	z-index: 15
}

.rp-content {
	margin: 30px auto 50px;
	width: 260px;
	height: 464px;
	background: url("../images/packet-bg.png?v=u860dba6e") center 49px no-repeat;
	background-size: 100%;
	position: relative
}

.rp-content h3 {
	color: #e4cca1;
	text-align: center;
	margin: -16px auto 10px;
	width: 100px;
	background-color: #d74f43
}

.rp-content .ion-close-round {
	font-size: 24px;
	color: #d74f43;
	position: absolute;
	top: 45px;
	right: -32px;
	padding: 0 4px
}

.rp-content .line {
	clear: both;
	width: 100%;
	margin-top: 10px;
	border: 1px dashed #c4483c
}

.rp-list .rp-info {
	margin: 0 auto 20px;
	padding-top: 4px;
	width: 210px;
	height: 180px;
	background: url("../images/packet-info.png?v=u860dba6e") center no-repeat;
	background-size: 100%;
	text-align: center
}

.rp-list .rp-info p {
	padding-left: 30px;
	font-size: 16px;
	color: #454545;
	text-decoration: underline;
	text-align: left;
	line-height: 46px;
	margin: 0
}

.rp-list .rp-info p span {
	color: #bb4036;
	width: 80px;
	font-weight: 700;
	text-decoration: none;
	display: inline-block
}

.rp-list .rp-info a {
	margin-top: 6px;
	display: inline-block;
	background-color: #d74f43;
	padding: 2px 6px;
	color: #fff;
	border-radius: 2px
}

.rp-list .switch {
	margin-right: 10px;
	float: right;
	line-height: 30px;
	font-size: 16px;
	text-decoration: underline;
	color: #830c01
}

.rp-list .intro {
	height: 194px;
	overflow: scroll;
	padding: 0 13px
}

.rp-list .intro h5 {
	color: #e4cca1;
	font-size: 16px;
	font-weight: 700;
	margin: 0 0 5px
}

.rp-list .intro p {
	color: #ffdfdf
}

.rp-list .rank {
	padding: 0 20px;
	text-align: center
}

.rp-list .rank-head {
	color: #e4cca1;
	font-size: 16px;
	padding-bottom: 0
}

.rp-list .rank-head .col {
	padding-bottom: 0
}

.rp-list .rank-list {
	height: 164px;
	overflow: scroll;
	color: #ffdfdf
}

.rp-list .rank-list .row {
	border-top: 1px dashed #c4483c
}

.rp-list .rank-list .row:first-child {
	border-top: 0
}

.rp-list .rank .current {
	color: #e4cca1
}

.rp-win {
	background-position: center -2px
}

.rp-win .rp-info {
	margin: 0 auto 35px;
	padding-top: 4px;
	width: 210px;
	height: 129px;
	background: url("../images/packet-win.png?v=u860dba6e") center no-repeat;
	background-size: 100%;
	text-align: center
}

.rp-win .rp-info h1 {
	margin-top: 20px;
	color: #bb4036;
	font-size: 40px;
	font-weight: 700
}

.rp-win .rp-info h1 span {
	font-size: 22px
}

.rp-win .rp-info a {
	font-size: 16px;
	margin-top: 15px;
	display: inline-block;
	color: #bb4036;
	text-decoration: underline
}

.rp-win .rule {
	color: #ffdfdf;
	padding: 10px 15px
}

.rp-open {
	margin: 40px auto 50px;
	width: 260px;
	height: 357px;
	background: url("../images/packet-open.png?v=u860dba6e") center no-repeat;
	background-size: 100%;
	position: relative;
	text-align: center
}

.rp-open .ion-close-round {
	font-size: 24px;
	color: #d74f43;
	position: absolute;
	top: -9px;
	right: -32px;
	padding: 0 4px
}

.rp-open .btn-open {
	background: url("../images/packet-kai.png?v=u860dba6e") center no-repeat;
	background-size: 100%;
	display: inline-block;
	padding: 44px;
	margin-top: 60px
}

.rp-open .active {
	animation: rpRotate 1s infinite;
	-webkit-animation: rpRotate 1s infinite
}

.rp-open h3 {
	width: 94%;
	margin: 20px auto 12px;
	color: #e4cca1;
	font-weight: 700;
	font-size: 20px;
	overflow: hidden;
	white-space: nowrap
}

.rp-open p {
	padding: 0 10px;
	color: #ffdfdf
}

.rp-out {
	background-position: center 10px;
	height: 425px;
	padding: 155px 10px 0;
	text-align: center;
	position: relative
}

.rp-out h2 {
	color: #e4cca1;
	font-weight: 700
}

.rp-out p {
	color: #ffdfdf
}

.rp-out .link {
	position: absolute;
	right: 10px;
	bottom: 10px;
	line-height: 30px;
	color: #dbbe89;
	font-size: 16px;
	text-decoration: underline
}

.rp-fail {
	background-position: center 10px;
	height: 425px;
	padding: 165px 10px 0;
	text-align: center;
	position: relative
}

.rp-fail h2 {
	color: #e4cca1;
	font-weight: 700
}

.rp-fail .btn-again {
	display: inline-block;
	width: 200px;
	height: 41px;
	line-height: 41px;
	color: #454545;
	font-weight: 700;
	font-size: 20px;
	background: url("../images/packet-btn.jpg?v=u860dba6e") center no-repeat;
	background-size: 100%
}

@-webkit-keyframes rpRotate {
	from {
		transform: rotateY(0deg)
	}

	to {
		transform: rotateY(360deg)
	}
}

@keyframes rpRotate {
	from {
		transform: rotateY(0deg)
	}

	to {
		transform: rotateY(360deg)
	}
}

.lotter-view .lotter-bar {
	background-color: #3883ba;
	height: 44px
}

.lotter-view .has-header {
	top: 45px
}

.lotter-view .member-balance {
	padding: 6px;
	font-size: .7rem
}

.lotter-view .button.button-clear {
	font-size: .7rem
}

.lotter-view .button.button-clear .game-name {
	background: url("../images/back.png") center right no-repeat;
	padding-right: 1rem;
	background-size: .7rem;
	line-height: 1rem;
    font-weight: 600;
}

.lotter-view .perio {
	box-shadow: inset 0 1px 0 rgba(255,255,255,.32);
	background-color: rgba(255,255,255,0);
	height: 3rem;
	font-size: .5rem;
	line-height: 1.5rem
}

.lotter-view .perio .pre-perio .item {
	padding: 0 .25rem;
	background-color: transparent;
	color: #fff;
	border: 0;
	font-size: .7rem
}

.lotter-view .perio .pre-perio .item .span-fl {
	float: left;
	padding-right: 5px
}

.lotter-view .perio .pre-perio .item .ion-arrow-swap {
	font-size: 1.1rem;
	padding: 0 .3rem
}

.lotter-view .perio .pre-perio .item .span-pt {
	padding-top: 10px
}

.lotter-view .perio .cur-perio {
	padding: 0;
	box-shadow: inset 0 1px 0 rgba(255,255,255,.2);
	position: relative;
	line-height: 1.5rem
}

.lotter-view .perio .cur-perio .item {
	font-size: .7rem;
	padding: 0 .25rem;
	background-color: transparent;
	color: #fff;
	border: 0
}

.lotter-view .perio .cur-perio .item .time {
	box-shadow: inset 1px 1px 0 rgba(250,250,250,.22),0 1px 0 rgba(0,0,0,.22);
	background-color: rgba(250,250,250,.15);
	padding: 1px 3px;
	border-radius: 2px 2px 2px 2px
}


.nums-wrap {
	height: 1.1rem;
}

.nums-wrap .equal {margin: 0 5px}

.result-wrap {
	height: 1.4rem;
}

.lotter-view .lottery-bet {
	height: 100px
}

.lotter-view .lottery-bet .active a {
	color: #fff;
	box-shadow: inset -1px 3px 7px rgba(0,0,0,.34);
	background-color: rgba(0,0,0,.23);
	border: 0
}

.lotter-view .lottery-bet .item {
	box-shadow: inset 0 1px 0 rgba(255,255,255,.2),0 -1px 0 rgba(0,0,0,.23);
	padding: 8px 5px;
	background: rgba(250,250,250,.11);
	text-align: center;
	position: relative;
	border: 0;
	display: block;
	font-size: .7rem
}

.lotter-view .lottery-bet .item .smallround {
	width: 7px;
	height: 7px;
	border-radius: 25px;
	background-color: rgba(0,0,0,.3);
	position: absolute;
	left: 5px;
	top: 5px;
	border-bottom: 1px solid rgba(250,250,250,.5);
	box-shadow: inset 0 -1 1px #fafafa,0 0 1px rgba(0,0,0,.5)
}

.lotter-view .lottery-bet .item .smallround.menus-choose {
	background-color: #fffb22;
	border-bottom: none
}

.lotter-view .lottery-bet .sub-Navs .row .sub-col {
	margin: 0;
	padding: 0 1px 0 0
}

.lotter-view .lottery-bet .sub-Navs .row .sub-col a {
	font-size: .7rem
}

.lotter-view .lottery-bet .sub-Navs .row .sub-col.navActive a {
	margin: 0;
	background-color: rgba(255,255,255,.25)!important;
	border: 0;
	color: rgba(0,0,0,.83)
}

.lotter-view .lottery-bet .sub-Navs .row .sub-col .item {
	margin: 0;
	box-shadow: none;
	background: rgba(250,250,250,.11);
	text-align: center;
	position: relative;
	border: 0;
	display: block;
	height: 30px
}

.lotter-view .lottery-bet .lottery-menu {
	box-shadow: 1px 0 0 rgba(255,255,255,.48);
	background-color: rgba(0,0,0,.21);
	position: absolute;
	right: 0;
	bottom: 0;
	left: 0;
	overflow: hidden;
	margin-top: 0;
	padding-top: 1px;
	margin-bottom: -1px;
	height: auto;
	top: 3.9rem;
	width: 3.7rem
}

.lotter-view .lottery-bet .bet-view {
	top: 3.9rem;
	left: 3.75rem
}

.group7 .lottery-bet .bet-view, .group7 .lottery-bet .lottery-menu {
	top: 3rem;
}

.group7 .span-pt {
	padding-top: 0px!important;
}

.lotter-view .lottery-bet .bet-view .close-pan {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background-color: rgba(0,0,0,.35);
	z-index: 1
}

.lotter-view .lottery-bet .bet-view .close-pan .tip {
	text-align: center;
	margin-top: 4rem;
	font-size: 1rem
}

.lotter-view .lottery-bet .tab-content .scroll-content.close .bet {
	text-shadow: 0 1px 0 rgba(255,255,255,.23);
	color: rgba(0,0,0,.8)
}

.lotter-view .lottery-bet .row {
	padding: 0;
	margin: 0
}

.lotter-view .lottery-bet .row.end {
	border-bottom: solid 1px rgba(255,255,255,.2)
}

.lotter-view .lottery-bet .row .col {
	text-align: center;
	padding: .4rem 0;
	font-size: .92rem
}

.lotter-view .lottery-bet .row .bet {
	position: relative;
	background: rgba(255,255,255,.02);
	text-shadow: 0 1px 0 rgba(255,255,255,.23);
	font-weight: bolder;
	color: rgba(0,0,0,.8);
	box-shadow: inset -1px 1px 1px rgba(255,255,255,.2)
}

.lotter-view .lottery-bet .row .bet.ban-bo {
	padding: 0
}

.lotter-view .lottery-bet .row .bet.ban-bo .bet-content {
	margin-left: .3rem;
	padding: 1rem 0;
	float: left
}

.lotter-view .lottery-bet .row .bet.ban-bo .bet-item {
	padding: 1rem 0;
	margin-left: 5px;
	float: left;
	line-height: 1.4rem
}

.lotter-view .lottery-bet .row .bet.ban-bo .bet-qiu {
	padding-top: .3rem;
	padding-bottom: .1rem;
	text-align: right;
	margin-right: .1rem;
	width: 7rem;
	float: right
}

.lotter-view .lottery-bet .row .bet.ban-bo .bet-qiu span {
	color: rgba(0,0,0,.8)
}

.lotter-view .lottery-bet .row .bet.ban-bo .bet-qiu .round-3 {
	line-height: 1.2rem;
	margin-right: 0;
	margin-bottom: .2rem;
	width: 1.2rem;
	height: 1.2rem;
	color: #000;
	display: inline-block;
	text-align: center;
	font-size: .7rem
}

.lotter-view .lottery-bet .row .bet.ban-bo .bet-qiu .round-3.red {
	background-image: url("../images/default/6h/red.png");
	background-size: 1.2rem
}

.lotter-view .lottery-bet .row .bet.ban-bo .bet-qiu .round-3.blue {
	background-image: url("../images/default/6h/blue.png");
	background-size: 1.2rem
}

.lotter-view .lottery-bet .row .bet.ban-bo .bet-qiu .round-3.green {
	background-image: url("../images/default/6h/green.png");
	background-size: 1.2rem
}

.lotter-view .lottery-bet .row .special {
	padding: .2rem 0
}

.lotter-view .lottery-bet .row .special .bet-item {
	padding-left: 0;
	display: block;
	line-height: .9rem;
	width: 100%
}

.lotter-view .lottery-bet .row .special .bet-content {
	padding-left: 0;
	display: block;
	line-height: 1.4rem
}

.lotter-view .lottery-bet .row .lh4,.lotter-view .lottery-bet .row .lh14,.lotter-view .lottery-bet .row .bet-round {
	line-height: 1.4rem
}

.lotter-view .lottery-bet .row .bet-round span {
	color: rgba(0,0,0,.8)
}

.lotter-view .lottery-bet .row .bet-content {
	display: inline-block;
	line-height: 1.4rem
}

.lotter-view .lottery-bet .row .bet-content.pd3 {
	margin-left: .3rem
}

.lotter-view .lottery-bet .row .bet-content .round-dit {
	display: inline-table
}

.lotter-view .lottery-bet .row .bet-item {
	display: inline-block;
	padding-left: 5px;
	font-size: .65rem;
	color: rgba(0,0,0,.5);
	min-width: 30px;
}

.lotter-view .lottery-bet .row .bet-bottom {
	border-bottom: 1px solid rgba(11,10,10,.23)
}

.lotter-view .lottery-bet .row .ok {
	background-color: rgba(255,255,255,.01);
	color: rgba(0,0,0,.8);
	font-weight: bolder;
	box-shadow: inset -1px 1px 1px rgba(255,255,255,.2)
}

.lotter-view .lottery-bet .row .bet-choose {
	background: rgba(0,0,0,.4);
	text-shadow: 0 0 0 rgba(255,255,255,.23);
	color: #fff;
	box-shadow: inset 0 1px 5px rgba(0,0,0,.3)
}

.lotter-view .lottery-bet .row .bet-choose .bet-item {
	color: #fff
}

.lotter-view .lottery-bet .row .bet-choose .bet-content span {
	color: rgba(0,0,0,.8)
}

.lotter-view .lottery-bet .row .bet-choose span {
	font-weight: 700
}

.lotter-view .lottery-bet .title,.lotter-view .lottery-bet .title-no {
	box-shadow: inset 0 1px 0 rgba(255,255,255,.2)
}

.lotter-view .hkc-bet-view .lottery-menu,.lotter-view .hkc-bet-view .bet-view {
	top: 3.1rem
}

.lotter-view .bar-footer {
	background-color: rgba(0,0,0,.39);
	height: 74px
}

.lotter-view .bar-footer .row {
	padding: 0;
	font-size: 16px;
	text-align: center
}

.lotter-view .bar-footer .row .pb5 {
	padding-bottom: 5px
}

.lotter-view .bar-footer .row .button {
	padding: 2px 4px 1px;
	min-width: 60px;
	min-height: 56px;
	font-size: 16px;
	font-weight: 700;
	line-height: 37px;
	color: #fff;
	border: 0
}

.lotter-view .bar-footer .row .button-bet {
	background-color: #d3563e;
	box-shadow: inset 0 0 3px rgba(250,250,250,.23),1px 3px 4px rgba(0,0,0,.18)
}

.lotter-view .bar-footer .row .button-reset {
	background-color: #4574c1;
	box-shadow: inset 0 0 3px rgba(250,250,250,.23),1px 3px 4px rgba(0,0,0,.18)
}

.lotter-view .bar-footer .close-pan {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background-color: #000;
	z-index: 1;
	opacity: .5
}

.lotter-view .bar-footer .close-pan .tip {
	text-align: center;
	margin-top: 1.2rem;
	font-size: 1rem;
	font-weight: 700
}

.lotter-view .has-footer {
	bottom: 75px
}

.lotter-view .menu-right {
	left: auto
}

.lotter-view .menu-left .bar.bar-stable,.lotter-view .menu-right .bar.bar-stable {
	background: 0 0;
	background-image: linear-gradient(0deg,rgba(178,178,178,0),rgba(255,255,255,0)50%,transparent 50%)
}

.lotter-view .menu-left .item-content {
	padding: 10px;
	font-size: .75rem
}

.lotter-view .menu-left .item a, .lotter-view .menu-right .item a {
    background-color: transparent;
    color: #fff;
    font-weight: 700;
}

.lotter-view .bar-header .title {
	color: #fff!important;
    font-size: 1rem;
    font-weight: 700;
}

.lotter-view .menu-left .item,.lotter-view .menu-right .item {
	background-color: transparent;
	color: #fff;
	font-weight: 700;
	border-color: rgba(0,0,0,.15);
	box-shadow: inset 0 1px 1px rgba(250,250,250,.2)
}

.lotter-view .menu-left .item a,.lotter-view .menu-right .item a {
	background-color: transparent;
	color: #fff;
	font-weight: 700
}

.lotter-view .ion-mt {
	margin-top: 6px
}

.lotter-popup .popup {
	width: 90%;
	color: #000
}

.lotter-popup .popup .red {
	color: red
}

.lotter-popup .popup .popup-head {
	padding: 0
}

.lotter-popup .popup .popup-head .popup-title {
	border-bottom: 1px solid rgba(0,0,0,.15);
	font-size: .8rem;
	line-height: 1.8rem
}

.lotter-popup .popup .popup-body {
	padding: 0 0 0 10px;
	font-size: .7rem;
	line-height: 1rem
}

.lotter-popup .popup .popup-body .scroll-view {
	padding: 0;
	margin: 0;
	height: 9.5rem;
	/* max-height: 9.5rem; */
	overflow-y: hidden;
}

.lotter-popup .popup .popup-buttons {
	min-height: 2.5rem;
	padding: 0 10px
}

.lotter-popup .popup .popup-buttons button {
	height: 1.8rem;
	line-height: 1.8rem;
	font-size: .8rem;
	margin-bottom: .3rem;
	margin-top: .4rem
}

.lotter-popup .popup .scroll-bar-fade-out {
	opacity: 2!important
}

.lotter-popup .split-line {
	margin-top: 10px;
    border-top: 1px solid rgba(0,0,0,.15);
    padding-top: 10px;
    margin-right: 10px;
}

.role-poput .popup {
	width: 80%
}

.role-poput .popup .popup-head {
	padding: 0
}

.role-poput .popup .popup-head .popup-title {
	border-bottom: 1px solid rgba(0,0,0,.15);
	font-size: .8rem;
	line-height: 1.8rem
}

.role-poput .popup .popup-body {
	padding: 0 0 0 10px;
	margin: 5px 0;
	overflow-x: hidden
}

.role-poput .popup .popup-body .scroll-view {
	padding: 0;
	margin: 0
}

.role-poput .popup .popup-buttons {
	min-height: 2.5rem;
	padding: 0 10px
}

.role-poput .popup .popup-buttons button {
	height: 1.8rem;
	line-height: 1.8rem;
	font-size: .8rem;
	margin-bottom: .3rem;
	margin-top: .4rem
}

.role-poput .popup .typo {
	color: #000;
	font-size: 12px
}

.role-poput .popup .scroll-bar-fade-out {
	opacity: 2!important
}

.inputbox {
	position: relative;
	color: #fff;
}

.inputbox .cel {
	width: 100%
}

.inputbox .cel .textinput {
	text-decoration: none;
	position: absolute;
	height: 30px;
	padding-left: .5em;
	width: 100%
}

.inputbox .cel .textinput-mt {
	margin-top: -5%
}

.inputbox .cel .txdel {
	position: absolute;
	right: 5px;
	top: 1px;
	display: inline-block;
	width: 35px;
	height: 36px;
	color: #000;
	padding-top: 5px
}

.inputbox .text-yellow {
	color: #ffd200;
	font-weight: 700
}

.bar .buttons-left span {
	margin-right: 0;
	display: inline-block
}

.opacityBetView {
	opacity: .5
}

.side-item,.ball-item {
	text-align: center
}

.lottery-list {color: #fff}
.lottery-list .no-title {
	text-align: center;
	padding: 0;
	height: 1.5rem;
	line-height: 1.5rem;
	font-size: .6rem
}

.lottery-list .no-title.pt {
	margin-top: .5rem
}

.lottery-list .no-title .radiu {
	background: rgba(255,255,255,.2);
	padding: 0 0 0 .05rem;
	box-shadow: inset -1px 1px 0 0 rgba(255,255,255,.21),1px 1px 0 0 rgba(0,0,0,.13)
}

.lottery-list .tips {
	height: 1.5rem;
	line-height: 1.5rem;
	padding-left: 10px
}

.lottery-list .tips a {
	color: #fff;
	font-size: .6rem
}

.lottery-list .list {
	position: absolute;
	margin-bottom: 0;
	top: 1.5rem
}

.lottery-list .list.detail {
	top: 3.1rem
}

.lottery-list .list .row {
	padding: 0;
	font-size: .6rem
}

.lottery-list .list .row .tal {
	box-shadow: inset -1px 1px 0 0 rgba(255,255,255,.2),1px 1px 0 0 rgba(0,0,0,.1);
	margin-bottom: 6px;
	padding: 0 .05rem 0 0;
	background-color: rgba(255,255,255,.05)
}

.lottery-list .list .row .tal div {
	padding: .5rem .25rem
}

.lottery-list .list .row .tal.bet {
	color: #1c1d4b
}

.lottery-list .has-footer, .lottery-list.has-footer {
	bottom: 45px;
}

.lottery-list .bar-footer .row {
	font-size: .7rem;
	padding: 0
}

.lottery-list .bar-footer .bet {
	color: #4e6fa9
}

.count {
	font-size: .7rem
}

.count .row.tips {
	margin-top: 0;
	padding: .2rem .5rem
}

.count .row.tips .col {
	padding: 0;
	color: #fff;
	font-size: .65rem;
	line-height: 1.2rem
}

.count .row.tips .col.day {
	color: rgba(255,255,255,.41)
}

.count .title-no {
	background: rgba(248,234,234,.14);
	margin-top: .2rem;
	padding: 0
}

.count .title-no.detail {
	margin-top: 0
}

.count .title-no .tle {
	line-height: 1.2rem;
	padding: .2rem 0 .2rem 1px
}

.count .list {
	position: absolute;
	padding: 0;
	background: 0 0;
	top: 1.9rem;
	margin-bottom: 0
}

.count .list.detail {
	top: 3.3rem
}

.count .list.detail .row {
	padding: 0
}

.count .list.detail .row.day {
	padding-top: 0;
	padding-bottom: 0
}

.count .list .row {
	padding: 0;
	margin-top: 0;
	border-bottom: 1px solid rgba(0,0,0,.12)
}

.count .list .row .bet {
	color: #1c1d4b
}

.count .list .row .tal {
	padding: 0 1px 0 0
}

.count .list .row .tal .cont {
	padding: .25rem 0 .25rem .25rem
}

.count .list .row .tal .cont .item-data {
	line-height: .8rem
}

.count .list .row .tal .cont .item-data.double {
	line-height: 2rem
}

.count .bar-footer {
	padding: 0;
	background-color: rgba(0,0,0,.8)
}

.count .bar-footer .row {
	margin: 0;
	padding: 5px 0;
	font-size: 16px
}

.count .bar-footer .row .color-blue {
	color: #4e6fa9
}

.chang-long .selectLottery {
	display: table-cell;
	vertical-align: middle;
	padding: 0;
	line-height: 2.25rem
}

.chang-long select {
	margin: 0 10px
}

.chang-long .no-title {
	padding: 0;
	margin: 0;
	text-align: center;
	line-height: 1rem;
	font-size: .7rem
}

.chang-long .no-title .radiu {
	background: rgba(255,255,255,.2);
	padding: .5rem 0;
	box-shadow: inset -1px 1px 0 0 rgba(255,255,255,.21),1px 1px 0 0 rgba(0,0,0,.13)
}

.chang-long .list {
	position: absolute;
	top: 2.105rem;
	text-align: center;
}

.chang-long .list .row {
	padding: 0;
	font-size: .6rem;
	color:#fff;
}

.chang-long .list .row .tal {
	padding: .5rem 0;
	box-shadow: inset -1px 1px 0 0 rgba(255,255,255,.21),1px 1px 0 0 rgba(0,0,0,.13);
	margin-bottom: 6px;
	background-color: rgba(255,255,255,.05)
}

.lu-zhu{
	color: #fff;
}
.lu-zhu .ball-temp {
	top: 2.2rem
}

.lu-zhu select {
	width: 5.5rem
}

.lu-zhu .ball-temp .row {
	padding: 0;
	font-size: .6rem
}

.lu-zhu .ball-temp .row.title {
	margin: .5rem 0 0
}

.lu-zhu .ball-temp .row.title.mt0 {
	margin-top: 0
}

.lu-zhu .ball-temp .row .radiu {
	background: rgba(255,255,255,.2);
	padding: .5rem 0;
	box-shadow: inset -1px 1px 0 0 rgba(255,255,255,.21),1px 1px 0 0 rgba(0,0,0,.13)
}

.lu-zhu .ball-temp .row .selected {
	color: #fff;
	box-shadow: inset -1px 3px 7px rgba(0,0,0,.28)
}

.lu-zhu .ball-temp .content {
	padding: 0;
	text-align: center
}

.lu-zhu .ball-temp .content .row .following {
	background-color: rgba(255,255,255,.05);
	border-bottom: 1px solid rgba(250,250,250,.47);
	box-shadow: inset 1px 1px 0 rgba(250,250,250,.47);
	max-width: 2rem!important;
	padding: 0
}

.lu-zhu .ball-temp .content .row .following div:first-child {
	padding-top: 7px
}

.lu-zhu .ball-temp .content .row .following div:last-child {
	padding-bottom: 7px
}

.lu-zhu .ball-temp .middle {
	text-indent: 1em;
	line-height: 28px;
	font-size: .6rem;
	width: 100%;
	height: 30px;
	margin-left: 1px;
	margin-bottom: 0;
	border-right: 1px solid rgba(0,0,0,.5);
	background: 0 0;
	box-shadow: inset 0 1px 2px rgba(250,250,250,.2),-1px -1px 1px rgba(0,0,0,.2);
	border-bottom: 1px solid rgba(0,0,0,.2)
}

.lu-zhu .side-temp {
	top: 2.2rem
}

.lu-zhu .side-temp .row {
	padding: 0;
	font-size: .6rem
}

.lu-zhu .side-temp .row.title {
	margin: 0
}

.lu-zhu .side-temp .row .radiu {
	background: rgba(255,255,255,.2);
	padding: .5rem 0;
	box-shadow: inset -1px 1px 0 0 rgba(255,255,255,.21),1px 1px 0 0 rgba(0,0,0,.13)
}

.lu-zhu .side-temp .row .selected {
	color: #fff;
	box-shadow: inset -1px 3px 7px rgba(0,0,0,.28)
}

.lu-zhu .side-temp .content {
	padding: 0;
	text-align: center
}

.lu-zhu .side-temp .content .row {
	min-height: 5rem
}

.lu-zhu .side-temp .content .row .following {
	padding: 0;
	margin: 0;
	max-width: 2rem!important;
	background-color: rgba(255,255,255,.05);
	border-bottom: 1px solid rgba(250,250,250,.47);
	box-shadow: inset 1px 1px 0 rgba(250,250,250,.47)
}

.lu-zhu .side-temp .content .row .following div:first-child {
	padding-top: 7px
}

.lu-zhu .side-temp .content .row .following div:last-child {
	padding-bottom: 7px
}

.lu-zhu button {
	background-color: rgba(250,252,250,.1);
	padding: .15rem;
	border: 0;
	color: #fafafa;
	box-shadow: inset 0 0 3px rgba(250,250,250,.5),0 0 5px rgba(0,0,0,.5);
	border-radius: 2px 2px 2px 2px;
	width: 2rem;
	height: 1.3rem;
	line-height: .5rem;
	font-size: .6rem
}

.history {color: #fff;}
.history select {
	margin: 0 10px
}

.history .pd {
	padding: 0;
	line-height: 2.25rem;
	display: table-cell;
	vertical-align: middle
}

.history .title {
	text-align: center;
	padding: 0;
	margin: 0;
	font-size: .7rem;
	line-height: 1rem
}

.history .title .radiu {
	background: rgba(255,255,255,.2);
	padding: .5rem 0 .5rem 1px;
	box-shadow: inset -1px 1px 0 0 rgba(255,255,255,.21),1px 1px 0 0 rgba(0,0,0,.13)
}

.history .list {
	position: absolute;
	top: 4.325rem
}

.history .list .row {
	padding: 0;
	font-size: .7rem
}

.history .list .row .tal {
	padding: 0 1px 0 0;
	box-shadow: inset -1px 1px 0 0 rgba(255,255,255,.2),1px 1px 0 0 rgba(0,0,0,.1);
	margin-bottom: 6px;
	background-color: rgba(255,255,255,.05)
}

.history .list .row .tal.padding-left {
	padding-left: 10px
}

.history .list .row .tal .cont {
	padding: .5rem .05rem;
	text-align: center
}

.history .list .row .tal .cont.pd1 {
	padding: .1rem .05rem 0
}

.history .list .row .tal .cont.lh {
	line-height: 1rem
}

.history .list .row .tal .cont.lh1 {
	line-height: 1.25rem
}

.history .list .row .tal .cont .mt-20 {
	margin-top: -20px
}

.history .list .row .tal .cont .mt-15 {
	margin-top: -15px
}

.history .list .row .tal .cont .mt5 {
	margin-top: 5px
}

.limit select {
	margin: 0 10px
}

.limit .pd {
	padding: 0;
	line-height: 2.25rem;
	display: table-cell;
	vertical-align: middle
}

.limit .title {
	text-align: center;
	padding: 0;
	margin: 0;
	font-size: .7rem;
	line-height: 1rem
}

.limit .title .radiu {
	background: rgba(255,255,255,.2);
	padding: .5rem 0 .5rem 1px;
	box-shadow: inset -1px 1px 0 0 rgba(255,255,255,.21),1px 1px 0 0 rgba(0,0,0,.13)
}

.limit .list {
	position: absolute;
	top: 4.325rem
}

.limit .list .row {
	padding: 0;
	font-size: .7rem
}

.limit .list .row .tal {
	padding: 0 1px 0 0;
	box-shadow: inset -1px 1px 0 0 rgba(255,255,255,.2),1px 1px 0 0 rgba(0,0,0,.1);
	margin-bottom: 6px;
	background-color: rgba(255,255,255,.05)
}

.limit .list .row .tal.padding-left {
	padding-left: 10px
}

.limit .list .row .tal .cont {
	padding: .5rem .05rem;
	text-align: center
}

.limit .list .row .tal .cont.pd1 {
	padding: .1rem .05rem 0
}

.limit .list .row .tal .cont.lh {
	line-height: 1rem
}

.limit .list .row .tal .cont.lh1 {
	line-height: 1.25rem
}

.limit .list .row .tal .cont .mt-20 {
	margin-top: -20px
}

.transfer .show-txt {
	margin: 1rem 6% .5rem 0;
	padding: .5rem;
	border-radius: .25rem;
	background-color: rgba(250,250,250,.08);
	box-shadow: inset 0 0 3px rgba(250,250,250,.24),0 0 1px rgba(0,0,0,.5);
	font-size: .7rem
}

.transfer .show-txt p.show-money {
	font-weight: 700;
	text-align: center;
	font-size: 1.1rem
}

.transfer .show-txt p.show-money a {
	padding-left: .5rem
}

.transfer .item-button {
	margin: 0 6% 20px 0
}

.transfer .item-button button {
	text-align: center;
	border-color: rgba(250,250,250,0);
	background-color: #227add;
	color: #fff;
	min-height: 2.35rem;
	top: .5rem;
	font-weight: 700;
	font-size: 1rem
}

.transfer .content {
	font-size: .7rem;
	margin: 0 0 0 1rem
}

.transfer .content div {
	padding-bottom: .5rem
}

.transfer .content .row {
	position: absolute;
	padding: 0 0 .5rem;
	display: block;
	clear: both
}

.transfer .content .row .item {
	border: none;
	background-color: rgba(250,250,250,.13);
	color: #fff;
	float: left;
	text-align: center;
	padding: .5rem .25rem;
	font-size: .8rem;
	font-weight: 700;
	margin: 0 2% 2% 0;
	width: 30%;
	height: 2.05rem;
	border-bottom: 1px solid rgba(0,0,0,.3);
	border-right: 1px solid rgba(0,0,0,.3);
	box-shadow: inset 0 1px 1px rgba(250,250,250,.3),0 0 5px rgba(0,0,0,.15);
	border-radius: .25rem .25rem .25rem .25rem
}

.transfer .content .row .item.pd {
	padding: .6rem .25rem
}

.transfer .content .row .item input[type="tel"] {
	width: 97%;
	padding: 0;
	height: 1.3rem;
	background-color: rgba(250,250,250,.01);
	color: #fff;
	font-weight: 700;
	font-size: .8rem;
	margin: -3px 0 0;
	position: absolute
}

.transfer .content .row .item input::-webkit-input-placeholder {
	line-height: 1.2em;
	color: rgba(0,0,0,.2)
}

.transfer .content .row .item.item-disabled {
	background: 0 0;
	color: rgba(0,0,0,.18)
}

.transfer .content .row .item.show-color {
	background: rgba(0,0,0,.4);
	border-right: 1px solid rgba(0,0,0,.3);
	color: #fff;
	box-shadow: inset 0 1px 5px rgba(0,0,0,.3),0 0 1px rgba(250,250,250,.5)!important
}

.transfer .content .row .item.wt {
	width: 94%;
	position: relative
}

.transfer .content .row .item .txdel {
	right: 5px;
	margin-top: -8px;
	padding-top: .4rem;
	width: 2.5rem;
	height: 2rem;
	position: absolute
}

@media only screen and (min-width:700px) {
	.lotter-view .lottery-bet .row .bet.ban-bo {
		padding: 0
	}

	.lotter-view .lottery-bet .row .bet.ban-bo .bet-content {
		margin-left: .3rem;
		padding: .7rem 0;
		float: left
	}

	.lotter-view .lottery-bet .row .bet.ban-bo .bet-item {
		padding: .7rem 0;
		margin-left: 5px;
		float: left;
		line-height: 1.4rem
	}

	.lotter-view .lottery-bet .row .bet.ban-bo .bet-qiu {
		padding: .7rem 0;
		text-align: right;
		margin-right: .1rem;
		width: 420px;
		float: right
	}

	.lotter-view .lottery-bet .row .bet.ban-bo .bet-qiu span {
		color: rgba(0,0,0,.8)
	}

	.lotter-view .lottery-bet .row .bet.ban-bo .bet-qiu .round-3 {
		line-height: 1.2rem;
		margin-right: .1rem;
		margin-bottom: .2rem;
		width: 1.2rem;
		height: 1.2rem;
		color: #000;
		display: inline-block;
		text-align: center;
		font-size: .7rem
	}

	.lotter-view .lottery-bet .row .bet.ban-bo .bet-qiu .round-3.red {
		background-image: url("../images/default/6h/red.png");
		background-size: 1.2rem
	}

	.lotter-view .lottery-bet .row .bet.ban-bo .bet-qiu .round-3.blue {
		background-image: url("../images/default/6h/blue.png");
		background-size: 1.2rem
	}

	.lotter-view .lottery-bet .row .bet.ban-bo .bet-qiu .round-3.green {
		background-image: url("../images/default/6h/green.png");
		background-size: 1.2rem
	}
}

@media only screen and (max-width:320px) {
	.round-9 {
		margin-right: 0
	}
}
/** 首页背景图片 */
.skin_blue .pane, .skin_blue .view {
    background: url("../images/bg2.jpg");
    background-size: cover;
}

.skin_red .pane, .skin_red .view {
    background: url("../images/bg3.jpg");
    background-size: cover;
}

/** 注册输入框placeholder文字颜色 */
.skin_red input::-webkit-input-placeholder {
    color: #e1d4d4!important;;
}

/** 游戏区背景图片 */
/* .skin_blue .lottery .view, .skin_blue .lottery .pane {
	background: #5d9dc0 url("../images/bg2.jpg")!important;
	background-size: cover!important;
	color: #fff
}

.skin_red .lottery .view, .skin_red .lottery .pane {
	background: #5d9dc0 url("../images/bg3.jpg")!important;
	background-size: cover!important;
	color: #fff
} */


/** 游戏区底部封盘样式 */
.skin_blue .lotter-view .bar-footer .close-pan {
	background-color: #5a97c3;
	opacity: .8;
	color: #f3efef
}

.skin_red .lotter-view .bar-footer .close-pan {
	background-color: #8d476d;
	opacity: .8;
	color: #f3efef
}
