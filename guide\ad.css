h3,ul,li,h4{
	padding: 0;
	margin: 0;
}
img{
	border: none;
}
li{list-style: none;}
table{
	border-collapse:collapse;
    width:100%;
}
.opacity{
	background: #000;
	filter:alpha(opacity=80);
	opacity: 0.8;
}
.floatLeft{
	float: left;
}
.floatRight{
	float: right;
}
.hide{
	display: none;
}
.ad_container{
	margin: 2px 0;
	background: url(./greybg_bg.png) repeat-x;
	padding-top:21px; 
}
.game,.novel,.menu,.content{
	width: 955px;
	margin: 0 auto;
	overflow: hidden;
	padding-top: 5px;
}
.menu,.content{
	border: 1px solid #ccc;
	padding:0;
	background: #fff;
}
.content{
	border-top: none;
}
/*导航菜单*/
.menu p{
	float: left;
	display: inline-block;
	_display : inline;
	cursor: pointer;
	height: 38px;
	line-height: 38px;
	border-right: 1px solid #ccc;
	padding: 1px;	
	text-align: center;
}
.menu span{
	display: inline-block;
	cursor: pointer;
	height: 38px;	
	padding: 0 2em;
	background: #fff;
	text-align: center;
}
#site_nav.on{
	background: #88B8DA;
	color: #fff;
}
#game_nav.on{
	background: #DA9988;
	color: #fff;
}
#vedio_nav.on{
	background: #db8;
	color: #fff;
}
#novel_nav.on{
	background: #9d8;
	color: #fff;
}
/*网址导航*/
#guide{
	font-size: 13px;
}
.guide_table{
	width: 100%;
	margin: 20px 0;	
}
.guide_table td{
	padding: 4px 0;
	text-align: center;
	line-height: 28px;
}
.guide a:link,.guide a:visited,.guide a:hover,.guide a:active{
	color: #666;
}
.guide a:hover{
	text-decoration: underline;
}
.guide-btn{
	display: inline-block;
	width: 158px;
	height: 28px;
	line-height: 28px;
	border: 1px solid #ccc;
	background: url(./greybg_btn.png) repeat-x;	
	color: #666;
	text-align: center;
	font-size: 13px;
}
.gicon{
	background: url(./siteIco.png) 0 0;
	padding-right:16px;
	margin-right: 10px; 
}
.ico_2{background-position: -16px 0;}
.ico_3{background-position: -32px 0;}
.ico_4{background-position: -48px 0;}
.ico_5{background-position: -64px 0;}
.ico_6{background-position: 0 -16px;}
.ico_7{background-position: -16px -16px;}
.ico_8{background-position: -32px -16px;}
.ico_9{background-position: -48px -16px;}
.ico_10{background-position: -64px -16px;}

.groups {
	width: 690px;
	border-bottom: 1px solid #ccc;
	margin-left: 20px;
	padding-bottom: 15px;
	margin-bottom: 15px; 
}
.last{
	border-bottom: none;
}
.coolsite-item{
	position: relative;
	overflow: hidden;
	line-height: 26px;
}
.coolsite-item span{
	padding: 0 6px;
}
.g_fl a:link,.g_fl a:visited,.g_fl a:hover,.g_fl a:active{
	color: #0F6698;
	text-decoration: none;
}
.g_fl{
	float: left;
	color: #0F6698;
	font-weight: 400;
	font-size: 13px;
}
.g_fr{
	float: right;
}
.img_ad{
	width: 222px;
	_width:227px;
}
.img_ad img{
	width: 220px;
	float: left;
	margin-bottom: 25px;
}
/*游戏*/
.game_list{
	overflow: hidden;
}
.game .game_list{
	padding-left: 11px; 	
}
.game_list li{
	float: left;
	margin:  0 4px 4px 0;
	position: relative;
	height: 132px;
	overflow: hidden;
}
.gameinfo{
	position: absolute;
	bottom: 0;
	left: 0;
	width: 230px;
	text-align: left;
	height: 24px;
	line-height: 24px;
	color: #fff;
	font-size: 12px;
	font-weight: 400;
	text-indent: 5px;
}
.gameinfo .date{
	position: absolute;
	right: 0;
	*top:-2px;
}
#game h3{
	text-align: left;
	padding: 15px 0 15px 10px;
	color: #CD0202;
	clear: both;
}
.game_guide_img{
	border-bottom: 3px solid #f00;
	margin-top: 10px;
}


/*在线视频秀*/
.liveshow_section_1,.liveshow_section_3,.liveshow_section_4{
	margin: 20px 0 0 25px;
}
.liveshow li{
	width: 160px;
	height: 120px;
	margin: 0 12px 10px 0;
	position: relative;
}
.liveshow_section_3 li,.liveshow_section_4 li{
	margin: 0 26px 10px 0;
	height: auto;
	_margin: 0 25px 10px 0;
}
.liveshow .firstimg{
	height: 250px;
	_height: 262px;
	width: 388px;
}
.girlinfo-mask{
	position: absolute;
	left: 0;
	bottom: 0;
	height: 50px;
	width: 160px;
    filter:alpha(opacity=20 finishopacity=80 style=1 startx=0,starty=0,finishx=0,finishy=50) progid:DXImageTransform.Microsoft.gradient(startcolorstr=#ffffff,endcolorstr=#000000,gradientType=0);
    -ms-filter:alpha(opacity=20 finishopacity=80 style=1 startx=0,starty=0,finishx=0,finishy=50) progid:DXImageTransform.Microsoft.gradient(startcolorstr=#ffffff,endcolorstr=#000000,gradientType=0);
	background:-moz-linear-gradient(top, rgba(255,255,255, 0.2), rgba(0, 0, 0, 0.8));
	background:-ms-linear-gradient(top, rgba(255,255,255, 0.2), rgba(0, 0, 0, 0.8));
	background:-webkit-gradient(linear, 0 0, 0 bottom, from(rgba(255,255,255, 0.2)), to(rgba(0, 0, 0, 0.8)));
	background:gradient(linear, 0 0, 0 bottom, from(rgba(255,255,255, 0.2)), to(rgba(0, 0, 0, 0.8)));   
	color: #fff;
}
.girlinfo{
	position: absolute;
	left: 0;
	bottom: 0;
	height: 35px;
	width: 160px;
	color: #fff;
	font-size: 12px;
	text-align: left;
}
.firstimg .girlinfo-mask,.firstimg .girlinfo{
	width: 388px;
	height: 40px;
	padding: 20px 0;
	line-height: 160%;
	color: #ED4100;
	font-size: 16px;
}
.liveshow_section_1 .girlinfo a{
	margin-left: 10px;
}
.girlinfo h3{
	font-size: 14px;
	text-indent: 10px;
}
.girlinfo a:link,.girlinfo a:visited,.girlinfo a:hover,.girlinfo a:active{
	color: #fff;
	text-decoration: none;
}
.liveshow_section_3 a:link,.liveshow_section_3 a:visited,.liveshow_section_3 a:hover,.liveshow_section_3 a:active{
	color: #333;
}
.firstimg img{
	width: 388px;
	height: 250px;
}
.firstimg .icon-play{
	display: inline-block;
	background: url(./icon.png) 0 -99px;
	height: 42px;
	width: 44px;
	float: left;
	margin: 0 10px 0 20px;
}

.liveshow_section_3 .girlinfo{
	position: static;
	color: #333;
	text-indent: 0;
	line-height: 40px;
	font-size: 13px;
}
.family-h3{
	width: 904px;
	margin: 0 auto;
	color: #FC8F6E;
	text-align: left;
	padding-top:16px; 
	border-top: 1px solid #ccc;
}


/*小说*/
.bookshow img{
	width:120px;
	height: 150px;
}
.novel{
	margin-bottom: 25px; 
}
.bookshow ul{
	margin: 23px 13px 0;
	width: 680px;
}
.bookshow li{
	overflow: hidden;
	padding-left: 10px;
}
.book-detail{
	width: 335px;
}
.bookshow2 .book-detail{
	width: 323px;
	_width: 310px;
	height: 183px;
	background: #f2f2f2;
	margin-right: 12px;
	margin-bottom: 12px;
}
}
.bookshow2 .book-detail div{
	height: 183px;
}
.book-detail-c{
	width: 168px;
	position: relative;
	height: 150px;
	text-align: left;
	font-size: 12px;
	margin-left: 1em;
	line-height: 140%;
	color: #999;
}
.book-detail-c h4{
	margin-bottom:10px; 
	color: #000;
}
.linkToBK{
	display: inline-block;
	width: 60px;
	height: 22px;
	line-height: 22px;
	text-align: center;
	color: #68BB99;
	font-size: 12px;
	position: absolute;
	bottom: 0;
	left: 0;
	border: 1px solid #ccc;
	background: url(./greybg_btn.png);
}
.book-detail-a{
	width: 120px;
}
.book-detail-a a{
	*float: left;
}
.linkToBK-name{
	width: 118px;
	height: 30px;
	line-height: 30px;
	border: 1px solid #ccc;
	font-size: 12px;
	background: url(./greybg_btn.png);
	color: #999;
	display: block;
}
.s-line{
	border-bottom: 1px solid #ccc;
	margin: 20px 0;
	height: 1px;
}
.bookshow2 .s-line{
	border-bottom:none;
	margin: 10px 0;
}

.novel-type-nav{
	background: #f7f7f7;
	height: 30px;
	line-height: 30px;
	border: 1px solid #ccc;
	border-left: none;
	border-right: none;
	font-size: 14px;
	text-align: left;
	text-indent: 24px;
}
.novel-type-nav span{
	color: #68BB99;
	cursor: pointer;
}
.novel-type-nav code{
	margin: 0 10px;
	font-size: 10px;
}
.bookSortList{
	text-align: left;
	font-size: 13px;
	width: 245px;
	border-left: 1px solid #ccc;
	padding-bottom: 1000px;
	margin-bottom: -1000px; 
	margin-top: 20px; 
	text-indent: 20px; 
}
.bookSortList h4{
	color: #68BB99;
	font-size: 16px;
	margin-bottom: 12px;
}
.bookSortList li{
	line-height: 140%;
}
.bookSortList span,.bookshow2 span{
	color: #999;
	margin-right: 1em;
}
.bookSortList a:link,.bookSortList a:visited,.bookSortList a:hover,.bookSortList a:active{
	color: #666;
}
.bookshow2 a:link,.bookshow2 a:visited,.bookshow2 a:hover,.bookshow2 a:active{
	color: #999;
}
.novel-type-nav a:link,.novel-type-nav a:visited,.novel-type-nav a:hover,.novel-type-nav a:active{
	color: #68BB99;
}
.bookSortList a:hover{
	text-decoration: underline;
}
.bookshow2 p{
	margin-bottom: 9px;
}

#cnzz_stat_icon_1252910590{display: none;}