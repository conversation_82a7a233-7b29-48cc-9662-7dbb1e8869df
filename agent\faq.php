<?php include('../data/comm.inc.php');    include('../data/agentvar.php');  include('../func/func.php'); include('../func/agentfunc.php');  include('../include.php'); include('./checklogin.php');  switch($_REQUEST['xtype']){      case "show": 	     $msql->query("select * from `$tb_faq`  order by time desc"); 		 $i=0; 		 while($msql->next_record()){ 		     $news[$i]['time'] = date("m-h H:i",$msql->f('time')); 			 $news[$i]['content'] = $msql->f('content'); 			 $news[$i]['title'] = $msql->f('title'); 			 $i++; 		 } 		 $tpl->assign('news',$news); 	     $tpl->display("faq.html"); 	 break; }