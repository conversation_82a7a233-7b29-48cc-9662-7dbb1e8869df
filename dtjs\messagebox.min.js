/***********************************************************************************************************************
MessageBox - A jQuery Plugin to replace Javascript's window.alert(), window.confirm() and window.prompt() functions
    Author          : Gaspare Sganga
    Version         : 2.2.1
    License         : MIT
    Documentation   : http://gasparesganga.com/labs/jquery-message-box/
***********************************************************************************************************************/
!function(e,t){function o(){!h&&y.length&&n(y.shift())}function n(o){var n=o.settings;h=o;var a=e("<div>",{class:"messagebox_overlay"}).css(x.overlay).appendTo("body"),r=e("<div>",{class:"messagebox_spacer"}).css(x.spacer).appendTo(a),c=e("<div>",{class:"messagebox"}).addClass(n.customClass).css(x.messagebox).data("instance",o).appendTo(a);n.width&&c.outerWidth(n.width);var u=e("<div>",{class:"messagebox_content",html:n.message}).css(x.content).appendTo(c);if(n.input!==!1&&n.input!==t&&null!==n.input){var l=e("<div>",{class:"messagebox_content_inputs",css:x.boxSizing}).appendTo(u);i(n.input).appendTo(l).first().trigger("focus")}e("<div>",{class:"messagebox_content_error",css:x.boxSizing}).hide().appendTo(u);var p=e("<div>",{class:"messagebox_buttons"}).css(x.buttons).appendTo(c);if(n.buttonDone){var f=e([]);"string"==typeof n.buttonDone?f=f.add(s("messagebox_button_done",_.buttonDoneName,{text:n.buttonDone,keyCode:_.keyCodeDone.concat(n.buttonFail?[]:_.keyCodeFail)},o)):e.each(n.buttonDone,function(e,t){f=f.add(s("messagebox_button_done",e,t,o))}),f.appendTo(p)}if(n.buttonFail){var f=e([]);"string"==typeof n.buttonFail?f=f.add(s("messagebox_button_fail",_.buttonFailName,{text:n.buttonFail,keyCode:_.keyCodeFail},o)):e.each(n.buttonFail,function(e,t){f=f.add(s("messagebox_button_fail",e,t,o))}),0===e.trim(n.buttonsOrder).toLowerCase().indexOf("d")?f.appendTo(p):f.prependTo(p)}var g=0,b=0-c.outerHeight()-_.topBuffer;"auto"===e.trim(n.top).toLowerCase()?(a.css("justify-content","center"),b-=e(window).height()):(a.css("justify-content","flex-start"),g=n.top,"%"===e.trim(n.top).toLowerCase().slice(-1)?c.css("max-height",100-parseInt(n.top,10)*_.maxHeightCoefficient+"%"):c.data("fRefreshMaxHeight",!0)),r.data("spacerTopMargin",b).css({height:0,"margin-top":b}).animate({height:g,"margin-top":0},n.speed,function(){d(c,e(window).height())})}function s(t,o,n,s){"string"==typeof n&&(n={text:n});var i=e("<button>",{class:t,text:n.text||""}).addClass(n.class).css(x.boxSizing).on("click",{name:o},f);return e.each(a(n.keyCode),function(e,t){s.keyCodes[t]=i}),i}function a(o){"number"!=typeof o&&"string"!=typeof o||(o=[o]);var n=[];return e.isArray(o)&&(n=e.map(o,function(e){return parseInt(e,10)||t})),n}function i(o){if(o===!0||"string"==typeof o)return c(e("<input>",{value:o===!0?"":o,type:"text"}),{autotrim:!0});if(e.isArray(o)){var n=e([]);return e.each(o,function(t,o){n=n.add(c(e("<input>",{value:o,type:"text"}),{autotrim:!0}))}),n}if(e.isPlainObject(o)){var n=e([]);return e.each(o,function(o,s){var a=r(o,s);if(s.label!==t){var i=e("<label>",{class:"messagebox_content_label",css:x.boxSizing,text:s.label});a.appendTo(i),n=n.add(i)}else n=n.add(a)}),n}return e(o)}function r(t,o){var n=e.trim(o.type).toLowerCase();switch(n){case"select":var s=c(e("<select>"),{name:t,title:o.title,autotrim:!1}),a=e.isArray(o.options)?o.options.reduce(function(e,t){return e[t]=t,e},{}):o.options;a||(l('No options provided for "'+t+'"'),a={"":"&nbsp;"});var i=!1;return e.each(a,function(t,n){var a=e("<option>",{value:t,html:n}).appendTo(s);o.default==t&&(a.prop("selected",!0),i=!0)}),i||(e("<option>",{value:"",text:o.title}).prop({disabled:!0,selected:!0,hidden:!0}).prependTo(s),s.find("option").css("color",s.css("color")),s.addClass("messagebox_content_input_selectplaceholder").prop("selectedIndex",0).one("change",function(){s.find("option").css("color",""),s.removeClass("messagebox_content_input_selectplaceholder")})),s;case"text":case"password":default:return c(e("<input>",{type:"password"===n?"password":"text",maxlength:o.maxlength,placeholder:o.title,value:o.default}),{name:t,title:o.title,autotrim:o.autotrim})}}function c(e,t){return t.autotrim!==!1&&e.on("blur",p),e.addClass("messagebox_content_input").css(x.boxSizing).attr({name:t.name,title:t.title})}function u(o){var n=[],s=[];if(o.find(".messagebox_content_inputs").find("input, select").each(function(){var t=e(this);n.push(t.attr("name")),s.push(t.val())}),!s.length)return t;var a={},i=!1;return e.each(n,function(e,o){return o===t?(i=!0,!1):void(a[o]=s[e])}),i&&1===s.length?s[0]:i?s:a}function d(e,t){e.data("fRefreshMaxHeight")&&e.css("max-height",t-e.offset().top*_.maxHeightCoefficient)}function l(e){e="jQuery MessageBox Warning: "+e,window.console.warn?console.warn(e):window.console.log&&console.log(e)}function p(t){var o=e(t.currentTarget);o.val(e.trim(o.val()))}function f(n){var s=e(n.currentTarget),a=n.data.name,i=s.closest(".messagebox"),r=i.closest(".messagebox_overlay"),c=r.children(".messagebox_spacer").first(),d=i.children(".messagebox_content").first(),l=d.children(".messagebox_content_error").first(),p=i.data("instance"),f=u(i),g=s.hasClass("messagebox_button_done")?p.settings.filterDone:p.settings.filterFail;l.hide().empty();var b="function"!==e.type(g)?e.Deferred().resolve():e.when(g(f,a)).then(function(t){if(t===!1)return e.Deferred().reject();var o=e.type(t);return"error"===o?e.Deferred().reject(t.message):"string"===o||"object"===o||"array"===o?e.Deferred().reject(t):e.Deferred().resolve()});b.then(function(){c.animate({height:0,"margin-top":c.data("spacerTopMargin")},p.settings.speed,function(){r.remove(),s.hasClass("messagebox_button_done")?p.deferred.resolve(f,a):p.deferred.reject(f,a),v.length?h=v.pop():(h=t,o())})},function(t){var o=e.type(t);"string"!==o&&"object"!==o&&"array"!==o||l.css("max-width",d.width()).append(t).slideDown(_.errorSpeed,function(){d.scrollTop(d.height())})})}function g(t){if(h){var o=e(t.currentTarget).width(),n=e(t.currentTarget).height();e(document).find(".messagebox").each(function(){var t=e(this);t.css("min-width",t.outerWidth()>o?o:""),d(t,n)})}}function b(e){if(h){var t=h.keyCodes[e.which];t&&(t.closest(".messagebox").find(".messagebox_content_input").trigger("blur"),t.trigger("click"))}}var m={buttonDone:"OK",buttonFail:t,buttonsOrder:"done fail",customClass:"",filterDone:t,filterFail:t,input:!1,message:"",queue:!0,speed:200,top:"25%",width:"auto"},x={overlay:{"box-sizing":"border-box",display:"flex","flex-flow":"column nowrap","align-items":"center",position:"fixed",top:"0",left:"0",width:"100%",height:"100%"},spacer:{"box-sizing":"border-box",flex:"0 1 auto"},messagebox:{"box-sizing":"border-box",flex:"0 1 auto",display:"flex","flex-flow":"column nowrap"},content:{"box-sizing":"border-box",flex:"0 1 auto","overflow-y":"auto"},buttons:{"box-sizing":"border-box",flex:"0 0 auto"},boxSizing:{"box-sizing":"border-box"}},h=t,v=[],y=[],_={buttonDoneName:"buttonDone",buttonFailName:"buttonFail",errorSpeed:200,keyCodeDone:[13],keyCodeFail:[27],maxHeightCoefficient:1.5,topBuffer:100};e.MessageBoxSetup=function(t){e.extend(!0,m,t)},e.MessageBox=function(t){e.isPlainObject(t)||(t={message:t});var s=e.Deferred(),a=e.extend(!0,{},m,t);a.top=e.trim(a.top).toLowerCase(),e(document.activeElement).not(".messagebox_content_input").trigger("blur");var i={deferred:s,keyCodes:{},settings:a};return a.queue?(y.push(i),o()):(h&&v.push(h),n(i)),s.promise()},e(function(){e(window).on("resize",g).on("keydown",b)})}(jQuery);