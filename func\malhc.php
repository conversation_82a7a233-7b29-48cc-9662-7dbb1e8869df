<?php
$ma['單'] = array(1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49);
$ma['雙'] = array(2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48);
$ma['单'] = array(1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49);
$ma['双'] = array(2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48);
$ma['大'] = array(25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49);
$ma['合單'] = array(1, 3, 5, 7, 9, 10, 12, 14, 16, 18, 21, 23, 25, 27, 29, 30, 32, 34, 36, 38, 41, 43, 45, 47, 49);
$ma['合雙'] = array(2, 4, 6, 8, 11, 13, 15, 17, 19, 20, 22, 24, 26, 28, 31, 33, 35, 37, 39, 40, 42, 44, 46, 48);
$ma['合单'] = array(1, 3, 5, 7, 9, 10, 12, 14, 16, 18, 21, 23, 25, 27, 29, 30, 32, 34, 36, 38, 41, 43, 45, 47, 49);
$ma['合双'] = array(2, 4, 6, 8, 11, 13, 15, 17, 19, 20, 22, 24, 26, 28, 31, 33, 35, 37, 39, 40, 42, 44, 46, 48);
$ma['尾大'] = array(5, 6, 7, 8, 9, 15, 16, 17, 18, 19, 25, 26, 27, 28, 29, 35, 36, 37, 38, 39, 45, 46, 47, 48, 49);
$ma['尾小'] = array(1, 2, 3, 4, 10, 11, 12, 13, 14, 20, 21, 22, 23, 24, 30, 31, 32, 33, 34, 40, 41, 42, 43, 44);
$ma['紅'] = array(1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46);
$ma['藍'] = array(3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48);
$ma['綠'] = array(5, 6, 11, 16, 17, 21, 22, 27, 28, 32, 33, 38, 39, 43, 44, 49);
$ma['红'] = array(1, 2, 7, 8, 12, 13, 18, 19, 23, 24, 29, 30, 34, 35, 40, 45, 46);
$ma['蓝'] = array(3, 4, 9, 10, 14, 15, 20, 25, 26, 31, 36, 37, 41, 42, 47, 48);
$ma['绿'] = array(5, 6, 11, 16, 17, 21, 22, 27, 28, 32, 33, 38, 39, 43, 44, 49);
$ma['1头'] = array(10, 11, 12, 13, 14, 15, 16, 17, 18, 19);
$ma['2头'] = array(20, 21, 22, 23, 24, 25, 26, 27, 28, 29);
$ma['3头'] = array(30, 31, 32, 33, 34, 35, 36, 37, 38, 39);
$ma['4头'] = array(40, 41, 42, 43, 44, 45, 46, 47, 48, 49);
$ma['0头'] = array(1, 2, 3, 4, 5, 6, 7, 0, 0);
$ma['1尾'] = array(1, 11, 21, 31, 41);
$ma['2尾'] = array(2, 12, 22, 32, 42);
$ma['3尾'] = array(3, 13, 23, 33, 43);
$ma['4尾'] = array(4, 14, 24, 34, 44);
$ma['5尾'] = array(5, 15, 25, 35, 45);
$ma['6尾'] = array(6, 16, 26, 36, 46);
$ma['7尾'] = array(7, 17, 27, 37, 47);
$ma['8尾'] = array(0, 18, 28, 38, 48);
$ma['9尾'] = array(0, 19, 29, 39, 49);
$ma['0尾'] = array(10, 20, 30, 40);
$ma['全部'] = array(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49);
$ma['小'] = array(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24);
$ma['家禽'] = array(01,03,04,09,11,12,13,15,16,21,23,24,25,27,28,33,35,36,37,39,40,45,47,48,49);
$ma['野獸'] = array(02,05,06,07,08,10,14,17,18,19,20,22,26,29,30,31,32,34,38,41,42,43,44,46);
$ma['前'] = array(05,06,07,08,09,10,17,18,19,20,21,22,29,30,31,32,33,34,41,42,43,44,45,46);
$ma['後'] = array(01,02,03,04,11,12,13,14,15,16,23,24,25,26,27,28,35,36,37,38,39,40,47,48,49);
$ma['家畜'] = $ma['家禽'];
$ma['野兽'] = $ma['野獸'];
$ma['后'] = $ma['後'];
$ma['紅單'] = array(1, 7, 13, 19, 23, 29, 35, 45);
$ma['紅雙'] = array(2, 8, 12, 18, 24, 30, 34, 40, 46);
$ma['紅大'] = array(29, 30, 34, 35, 40, 45, 46);
$ma['紅小'] = array(1, 2, 7, 8, 12, 13, 18, 19, 23, 24);
$ma['藍單'] = array(3, 9, 15, 25, 31, 37, 41, 47);
$ma['藍雙'] = array(4, 10, 14, 20, 26, 36, 42, 48);
$ma['藍大'] = array(25, 26, 31, 36, 37, 41, 42, 47, 48);
$ma['藍小'] = array(3, 4, 9, 10, 14, 15, 20);
$ma['綠單'] = array(5, 11, 17, 21, 27, 33, 39, 43);
$ma['綠雙'] = array(6, 16, 22, 28, 32, 38, 44);
$ma['綠大'] = array(27, 28, 32, 33, 38, 39, 43, 44);
$ma['綠小'] = array(5, 6, 11, 16, 17, 21, 22);
$ma['内围'] = array(09,10,11,12,13,16,17,18,19,20,23,24,25,26,27,30,31,32,33,34,37,38,39,40,41);
$ma['外围'] = array(01,02,03,04,05,06,07,08,14,15,21,22,28,29,35,36,42,43,44,45,46,47,48,49);
$ma['楼上'] = array(01,02,03,04,05,06,07,08,09,10,11,12,13,14,15,16,17,18,19,20,21,25,26,27,28);
$ma['楼下'] = array(22,23,24,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49);


function rwuhang($wh, $bml)
{
    $ny = array('金', '金', '火', '火', '木', '木', '土', '土', '金', '金', '火', '火', '水', '水', '土', '土', '金', '金', '木', '木', '水', '水', '土', '土', '火', '火', '木', '木', '水', '水', '金', '金', '火', '火', '木', '木', '土', '土', '金', '金', '火', '火', '水', '水', '土', '土', '金', '金', '木', '木', '水', '水', '土', '土', '火', '火', '木', '木', '水');
    $jiazhi = array('甲子', '乙丑', '丙寅', '丁卯', '戊辰', '己巳', '庚午', '辛未', '壬申', '癸酉', '甲戌', '乙亥', '丙子', '丁丑', '戊寅', '己卯', '庚辰', '辛巳', '壬午', '癸未', '甲申', '乙酉', '丙戌', '丁亥', '戊子', '己丑', '庚寅', '辛卯', '壬辰', '癸巳', '甲午', '乙未', '丙申', '丁酉', '戊戌', '己亥', '庚子', '辛丑', '壬寅', '癸卯', '甲辰', '乙巳', '丙午', '丁未', '戊申', '己酉', '庚戌', '辛亥', '壬子', '癸丑', '甲寅', '乙卯', '丙辰', '丁巳', '戊午', '己未', '庚申', '辛酉', '壬戌', '癸亥');
    $in = 0;
    foreach ($jiazhi as $key => $val) {
        if ($val == $bml) {
            $in = $key;
            break;
        }
    }
    $w = array();
    for ($i = 1; $i <= 49; $i++) {
        $index = 1922 - $i + $in-1;
        if ($ny[$index % 60] == $wh) {
            $t = $i < 10 ? '0' + $i : $i;
            $w[] = $t;
        }
    }
    return $w;
}
function wuhang($ma,$bml){
    $ny = array('金', '金', '火', '火', '木', '木', '土', '土', '金', '金', '火', '火', '水', '水', '土', '土', '金', '金', '木', '木', '水', '水', '土', '土', '火', '火', '木', '木', '水', '水', '金', '金', '火', '火', '木', '木', '土', '土', '金', '金', '火', '火', '水', '水', '土', '土', '金', '金', '木', '木', '水', '水', '土', '土', '火', '火', '木', '木', '水','水');
    $jiazhi = array('甲子', '乙丑', '丙寅', '丁卯', '戊辰', '己巳', '庚午', '辛未', '壬申', '癸酉', '甲戌', '乙亥', '丙子', '丁丑', '戊寅', '己卯', '庚辰', '辛巳', '壬午', '癸未', '甲申', '乙酉', '丙戌', '丁亥', '戊子', '己丑', '庚寅', '辛卯', '壬辰', '癸巳', '甲午', '乙未', '丙申', '丁酉', '戊戌', '己亥', '庚子', '辛丑', '壬寅', '癸卯', '甲辰', '乙巳', '丙午', '丁未', '戊申', '己酉', '庚戌', '辛亥', '壬子', '癸丑', '甲寅', '乙卯', '丙辰', '丁巳', '戊午', '己未', '庚申', '辛酉', '壬戌', '癸亥');
    $in = 0;
    foreach ($jiazhi as $key => $val) {
        if ($val == $bml) {
            $in = $key;
            break;
        }
    }
	//return (1922 - $ma + $in-1)%60;
	return $ny[(1922 - $ma + $in-1)%60];
	
}

function shengxiao($ma, $bml)
{
    $jiazhi = array('甲子', '乙丑', '丙寅', '丁卯', '戊辰', '己巳', '庚午', '辛未', '壬申', '癸酉', '甲戌', '乙亥', '丙子', '丁丑', '戊寅', '己卯', '庚辰', '辛巳', '壬午', '癸未', '甲申', '乙酉', '丙戌', '丁亥', '戊子', '己丑', '庚寅', '辛卯', '壬辰', '癸巳', '甲午', '乙未', '丙申', '丁酉', '戊戌', '己亥', '庚子', '辛丑', '壬寅', '癸卯', '甲辰', '乙巳', '丙午', '丁未', '戊申', '己酉', '庚戌', '辛亥', '壬子', '癸丑', '甲寅', '乙卯', '丙辰', '丁巳', '戊午', '己未', '庚申', '辛酉', '壬戌', '癸亥');
    $index = 0;
    foreach ($jiazhi as $key => $val) {
        if ($val == $bml) {
            $index = $key;
            break;
        }
    }
    $index = $index % 12 + 2;
    $ma = $ma % 12;
    $arr = array('鼠', '牛', '虎', '兔', '龍', '蛇', '馬', '羊', '猴', '雞', '狗', '豬');
	$in= 0 ;
    if ($index >= $ma) {
      $in = $index - $ma;
    } else {
       $in =  $index - $ma + 12;
    }
	if($in>=12) $in -=12;
	return $arr[$in];
}
function rshengxiao($sx, $bml)
{
    $jiazhi = array('甲子', '乙丑', '丙寅', '丁卯', '戊辰', '己巳', '庚午', '辛未', '壬申', '癸酉', '甲戌', '乙亥', '丙子', '丁丑', '戊寅', '己卯', '庚辰', '辛巳', '壬午', '癸未', '甲申', '乙酉', '丙戌', '丁亥', '戊子', '己丑', '庚寅', '辛卯', '壬辰', '癸巳', '甲午', '乙未', '丙申', '丁酉', '戊戌', '己亥', '庚子', '辛丑', '壬寅', '癸卯', '甲辰', '乙巳', '丙午', '丁未', '戊申', '己酉', '庚戌', '辛亥', '壬子', '癸丑', '甲寅', '乙卯', '丙辰', '丁巳', '戊午', '己未', '庚申', '辛酉', '壬戌', '癸亥');
    $index = 0;
    foreach ($jiazhi as $key => $val) {
        if ($val == $bml) {
            $index = $key;
            break;
        }
    }
    $index = $index % 12 + 2;
    switch ($sx) {
        case '鼠':
            $i = 1;
            break;
        case '牛':
            $i = 2;
            break;
        case '虎':
            $i = 3;
            break;
        case '兔':
            $i = 4;
            break;
        case '龍':
            $i = 5;
            break;
        case '蛇':
            $i = 6;
            break;
        case '馬':
            $i = 7;
            break;
        case '羊':
            $i = 8;
            break;
        case '猴':
            $i = 9;
            break;
        case '雞':
            $i = 10;
            break;
        case '狗':
            $i = 11;
            break;
        case '豬':
            $i = 12;
            break;
    }
    if ($index >= $i) {
        $arr[0] = $index - $i + 1;
    } else {
        $arr[0] = $index - $i + 13;
    }
    $arr[1] = $arr[0] + 12;
    $arr[2] = $arr[0] + 24;
    $arr[3] = $arr[0] + 36;
    if ($index == $i) {
        $arr[4] = $arr[0] + 48;
    }
    if ($arr[0] < 10) {
        $arr[0] = '0' + $arr[0];
    }
    return $arr;
}


function chu($v,$bc){
	return $v%$bc;
}

function duan7($tm){
	$duan=($tm-$tm%7)/7;
	if ($tm%7 != 0) $duan=$duan+1;
	return $duan;
}

function duan4($tm){
	
	if ($tm<=12) $duan=1;
	else if ($tm<=24) $duan=2;
	else if ($tm<=36) $duan=3;
	else $duan=4;
	return $duan;
}

function duan3($tm){
	
	if ($tm<=16) $duan=1;
	else if ($tm<=32) $duan=2;
	else $duan=3;
	return $duan;
}

function sanhex($tm,$bml){
	$shx[]="鼠龍猴";
	$shx[]="牛蛇雞";
	$shx[]="虎馬狗";
	$shx[]="兔羊豬";
	return $shx;
}
function liuhex($tm,$bml){
	$lhx[]="鼠牛";
	$lhx[]="虎豬";
	$lhx[]="兔狗";
	$lhx[]="龍雞";
	$lhx[]="蛇猴";
	$lhx[]="馬羊";
	return $lhx;
}


function sanhexs($v){
	$shx[]="鼠龍猴";
	$shx[]="牛蛇雞";
	$shx[]="虎馬狗";
	$shx[]="兔羊豬";
	foreach($shx as $a){
	   if(strpos("[".$a."]",$v)>0){
		   return $a;
	   }
	}
}
function liuhexs($v){
	$lhx[]="鼠牛";
	$lhx[]="虎豬";
	$lhx[]="兔狗";
	$lhx[]="龍雞";
	$lhx[]="蛇猴";
	$lhx[]="馬羊";
	foreach($lhx as $a){
	   if(strpos("[".$a."]",$v)>0){
		   return $a;
	   }
	}

}

function qhx($v){
	$arr = array('鼠', '牛', '虎', '兔', '龍', '蛇');
	if(in_array($v,$arr)){
	   return "前";
	}else{
	   return "后";
	}
}
function jyx($v){
	$arr = array( '牛', '馬', '羊','雞', '狗', '豬');
	if(in_array($v,$arr)){
	   return "家";
	}else{
	   return "野";
	}
}
function tdx($v){ //天地肖
	$arr = array( '牛', '兔', '龍', '馬','猴', '豬');
	if(in_array($v,$arr)){
	   return "天";
	}else{
	   return "地";
	}
}
function dsbx($v){ //单双笔
	$arr = array( '鼠', '龍', '蛇', '馬','雞', '豬');
	if(in_array($v,$arr)){
	   return "单";
	}else{
	   return "双";
	}
}

function yyx($v){ //阴阳
	$arr = array( '鼠', '龍', '蛇', '馬','狗', '豬');
	if(in_array($v,$arr)){
	   return "阴";
	}else{
	   return "阳";
	}
}
function mcx($v){ //美丑肖
	$arr = array( '兔', '龍', '蛇', '馬','羊', '雞');
	if(in_array($v,$arr)){
	   return "美";
	}else{
	   return "丑";
	}
}

function nnx($v){ //男女肖
	$arr = array( '鼠', '牛','虎', '龍',  '馬','猴', '狗');
	if(in_array($v,$arr)){
	   return "男";
	}else{
	   return "女";
	}
}

function sex($v){ //色肖
	$arr1 = array( '兔', '鼠',  '馬','雞');
	$arr2 = array( '蛇', '虎',  '猴','豬');
	if(in_array($v,$arr1)){
	   return "红";
	}else if(in_array($v,$arr2)){
	   return "蓝";
	}else{
	   return "绿";
	}
}

function qhlm($v){
	$arr = array(01,02,03,04,05,06,07,08,17,18,19,20,21,22,23,24,33,34,35,36,37,38,39,40);
	if(in_array($v,$arr)){
	   return "前";
	}else{
       return "后";
	}

}
function nwwm($v){
	$arr = array(09,10,11,12,13,16,17,18,19,20,23,24,25,26,27,30,31,32,33,34,37,38,39,40,41);
	if(in_array($v,$arr)){
	   return "内";
	}else{
       return "外";
	}

}
function lsxm($v){
	$arr = array(01,02,03,04,05,06,07,08,09,10,11,12,13,14,15,16,17,18,19,20,21,25,26,27,28);
	if(in_array($v,$arr)){
	   return "上";
	}else{
       return "下";
	}

}

function zym($v){
	$arr = array(01,02,03,04,08,09,10,11,15,16,17,18,22,23,24,29,30,31,36,37,38,43,44,45);
	if(in_array($v,$arr)){
	   return "左";
	}else{
       return "右";
	}

}


function gdm($v){
	$arr = array(01,04,07,09,10,12,17,18,19,25,26,27,28,29,30,34,35,36,37,39,44,45,47,48);
	if(in_array($v,$arr)){
	   return "高";
	}else{
       return "低";
	}

}

function txdjm($v){
	$arr = array(01,04,07,09,10,11,14,17,19,20,21,24,27,29,30,31,34,37,39,40,41,44,47,49);
	if(in_array($v,$arr)){
	   return "天玄";
	}else{
       return "地机";
	}

}

function rbose($tm)
{
    global $ma;
	if(in_array($tm,$ma["紅"])){
	    return "红";
	}else if(in_array($tm,$ma["藍"])){
	    return "蓝";
	}else{
	    return "绿";
	}
}

function lhds($tm){
	if($tm%2==0) return "双";
	else return "单";
}
function lhdx($tm){
	if($tm<=24) return "小";
	else return "大";
}
function hdx($tm){
	$hes= hs($tm);
	if($hes<=6) return "合大";
	else return "合小";
}
function hds($tm){
	$hes= hs($tm);
	return "合".lhds($hes);
}
function tous($tm){
	return ($tm - $tm%10)/10;
}
function weis($tm){
	return $tm%10;
}

function rbanbods($tm)
{
    global $ma;
	if(in_array($tm,$ma["紅"])){
	    return "红".lhds($tm);
	}else if(in_array($tm,$ma["藍"])){
	    return "蓝".lhds($tm);
	}else{
	    return "绿".lhds($tm);
	}
}


function rbanbodx($tm)
{
    global $ma;
	if(in_array($tm,$ma["紅"])){
	    return "红".lhdx($tm);
	}else if(in_array($tm,$ma["藍"])){
	    return "蓝".lhdx($tm);
	}else{
	    return "绿".lhdx($tm);
	}
}
