﻿#scrollpage{ min-width:1100px;}
.section{height:100%;overflow:hidden;}

/*------------------------- 百度搜索样式 -------------------------*/
.page{
	width:100%;
	height:100%;
	overflow:hidden;
}
.header{
	height:100px;
	background:#5482c4;
	width: 100%;
	position: absolute;
}
.header ul{
	width:1100px;
	padding:30px 0 0 0;
	margin:0 auto;
}
.header li{ float:left;}
.logo{
	width:250px;
	height:45px;
	background:url(./icons.png) no-repeat 248px -150px;
}
.header_nav{ margin:8px 0 0 50px;}
.header_nav a{
	display:block;
	font-size:18px;
	color:white;
	line-height:32px;
	padding:0 14px;
	float:left;
}
.header_nav a:hover{
	color:#fff;
	background:#234d8a;
}
.header_login{ font-size:14px; color:white; margin:18px 0 0 70px;}
.header_login a{ font-size:14px; color:white; margin:0 10px; cursor:pointer;}
.header_login a:hover{ color:yellow; text-decoration:underline;}
.login_enter{
	padding:0 0 0 22px;
	background:url(./icons.png) no-repeat left -193px;
}

.bd_search{ width:auto; margin:4.5% auto;}
.bd_logo{ text-align:center; margin:30px auto 0 auto;}
.bd_input{ float:left; height:48px; border:1px solid #1a95e3; margin:20px 0;}
.searchTop_txt{ width:490px; font-size:20px; line-height:50px; float:left; height:45px; border:0px; background:#fff; padding:0 0 0 10px;}
.searchTop_btn{ width:140px; float:left; height:48px; border:0px; background:#1a95e3; text-align:center; color:#fff; font-size:14px;cursor: pointer; font-weight:bold;}
.bd_link a{ font-size:14px; color:#313131; margin:0 20px 0 0;}
.bd_link a:hover{ color:red; text-decoration:underline;}
.bd_footer{ font-size:14px; color:#7d7d7d; line-height:28px; margin:50px 0 0 0; text-align:center;}
.bd_footer a{ font-size:14px; color:#7d7d7d; margin:0 20px 0 0;}
.bd_footer a:hover{ color:red; text-decoration:underline;}
.footer{ width:100%; position:absolute; bottom:0; height:55px; line-height:55px; background:#2a43a6;}
.footer li{ width:1100px; font-size:18px; color:white; margin:0 auto;}
.footer_ft01{ display:block; float:left;}
.footer_ft02{ display:block; float:right;}
.footer_ft02 span{ font-size:20px; color:#f8b551;}



/*------------------------- 各页头色样式 -------------------------*/
.green_title{ background:#4eb856;}
.green_bg{ background:#e4f1de;}
.green_ft{ color:#79d480;}
.green_tl dt{ background:url(./title_bg.png) no-repeat left top;}
.pic01 span{ display:block; width:170px; height:30px; background:url(./icons.png) no-repeat left top; margin:20px 0 0 0; text-indent:-9999px;}

.red_title{ background:#c23447;}
.red_bg{ background:#f6ebea;}
.red_ft{ color:#c85565;}
.red_tl dt{ background:url(./title_bg.png) no-repeat left -44px;}
.pic02 span{ display:block; width:170px; height:30px; background:url(./icons.png) no-repeat left -30px; margin:20px 0 0 0; text-indent:-9999px;}

.blue_title{ background:#5482c4;}
.blue_bg{ background:#eaf2ff;}
.blue_ft{ color:#7ba3dd;}
.blue_tl dt{ background:url(./title_bg.png) no-repeat left -88px;}
.pic03 span{ display:block; width:170px; height:30px; background:url(./icons.png) no-repeat left -60px; margin:20px 0 0 0; text-indent:-9999px;}

.orange_title{ background:#e7922d;}
.orange_bg{ background:#fef0df;}
.orange_ft{ color:#f7b15f;}
.orange_tl dt{ background:url(./title_bg.png) no-repeat left -132px;}
.pic04 span{ display:block; width:170px; height:30px; background:url(./icons.png) no-repeat left -90px; margin:20px 0 0 0; text-indent:-9999px;}

.green2_title{ background:#3cb19c;}
.green2_bg{ background:#e6fefa;}
.green2_ft{ color:#54c9b4;}
.green2_tl dt{ background:url(./title_bg.png) no-repeat left -176px;}
.pic05 span{ display:block; width:170px; height:30px; background:url(./icons.png) no-repeat left -120px; margin:20px 0 0 0; text-indent:-9999px;}




/*------------------------- 其它页样式 -------------------------*/
.page_header{ width:100%; height:98px; position:absolute; top:0; z-index:100;
background-image:url(./whiteopacity.png);

}
.page_header ul{ width:1100px; margin:0 auto;}
.page_headtitle{   width: auto;
  height: auto;
  float: left;
  /*margin-left: -75px;*/
  margin-top: 9px;}
.page_headlink{ font-size: 16px;
  width: auto;
  height: 70px;
  line-height: 80px;
  text-indent: 50px;
  float: right;
  margin-top: 10px;
  margin-right:123px;}
.page_headlink span{ color:white; font-weight:bold;}
.page_headlink a{ color:white; margin:0 10px;}
.page_headlink a:hover{ color:yellow; text-decoration:underline;}


.page_mainlink{ width:1200px; margin:110px auto 0 auto;}
.info{
	width:980px;
	position:absolute;
	top:90px;
	bottom:0px;
	z-index:99;
	overflow-x:hidden;
	overflow-y:auto;
}
.adpic{ width:auto; margin:54px 0 0 1090px; position:absolute;}
.page_mainlink dl{
	width:100%;
	margin:0 0 30px 0;
	float:left;
	position:relative;
	padding:0 0 0 8px;
}
.page_mainlink dt{
	width:143px;
	height:44px;
	font-size:20px;
	font-weight:bold;
	color:white;
	line-height:38px;
	text-align:center;
	position:absolute;
	margin:0 0 0 -8px;
	*margin:0 0 0 0;
	*left:0;
	*top:0;
}
.page_mainlink dd{
	width:134px;
	height:38px;
	line-height:38px;
	background:white url(./icons.png) no-repeat 120px -330px;
	border:1px solid #dcdcdc;
	text-indent:10px;
	float:left;
}
.page_mainlink dd a{ font-size:14px; color:#434343;}
.page_mainlink dd a:hover{ color:red; text-decoration:underline;}
.adpic a{ display:block; margin:0 0 15px 0;}


/*------------------------- 弹出窗样式 -------------------------*/
.win{ width:100%; height:100%; position:absolute; z-index:99999; display:none;}
.win_login{ width:100%; height:100%; position:absolute; z-index:88888;}
.div_login{ width:410px; height:320px; background:white; margin:16% auto 0 auto;}
.div_reg{ width:660px; height:500px; background:white; margin:10% auto 0 auto;}
.win_bg{ position:absolute; width:100%; height:100%; background:#000; z-index:77777; opacity: 0.5;filter:alpha(opacity=50);}

.win_title{
	width:100%;
	height:45px;
	background:#5482c4;
}
.win_title span{
	display:block;
	font-size:20px;
	font-weight:bold;
	color:white;
	line-height:45px;
	text-indent:15px;
	float:left;
}
.win_title a{
	display:block;
	width:23px;
	height:23px;
	float:right;
	text-indent:-9999px;
	background:url(./icons.png) no-repeat left -265px;
	margin:11px 12px 0 0;
	cursor:pointer;
}
.win_title a:hover{
	background:url(./icons.png) no-repeat -23px -265px;
}

.user_input{ width:260px; margin:40px auto;}
.user_input li{ height:40px; margin:0 0 25px 0;}
.input_te{ border:1px solid #999999; background:url(./icons.png) repeat-x left -368px;}
.input_te input{ width:210px;}
.input_te div{ font-size:16px; color:white; line-height:40px; margin:0 0 0 25px; float:left;}

.user_input span{ display:block; width:25px; height:25px; text-indent:-9999em; margin:8px 5px 0 10px; float:left;}
.user_input input{ font-size:20px; color:#a0a0a0; margin:8px 0 0 0; float:left; border:1px solid white;}
.user_input img{ display:block; margin:2px 10px 0 30px; float:left;}

.ico_user{ background:url(./icons.png) no-repeat left -213px;}
.ico_pass{ background:url(./icons.png) no-repeat left -238px;}

.bu_line{ margin:100px 0 0 0; *margin:40px 0 0 0;}
.reg_bu a{ display:block; width:202px; height:43px; text-indent:-9999em; margin:40px auto 0 auto; *margin:0 auto;}
.bu_login{ background:url(./reg_bu.jpg) no-repeat left top;}
.bu_login:hover{ background:url(./reg_bu.jpg) no-repeat left -43px;}
.bu_reg{ background:url(./reg_bu.jpg) no-repeat left -86px;}
.bu_reg:hover{ background:url(./reg_bu.jpg) no-repeat left -129px;}


.reg_title{ width:90%; border-bottom:2px solid #c9c9c9; margin:20px auto;}
.reg_titlepic{ width:45px; height:42px; background:url(./icons.png) no-repeat left -288px; margin:0 12px; text-indent:-9999em; float:left;}
.reg_title li{ font-size:24px; color:#199737; line-height:40px; margin:0 0 20px 0;}

.reg_te{ width:90%; margin:0 auto;}
.reg_te li{ font-size:16px; line-height:38px; color:#898989; margin:0 0 18px 0;}
.reg_te span{ display:block; width:100px; font-size:18px; color:#313131; text-align:right; float:left;}
.reg_te li input{ border-top:1px solid #b8b8b8; border-left:1px solid #b8b8b8; border-right:1px solid #cccccc; border-bottom:1px solid #cccccc; margin:0 15px 0 0; float:left;}
.reg_te li img{ display:block; float:left;}


.te01{ width:300px; height:34px; font-size:20px; line-height:34px; text-indent:5px; }
.te02{ width:120px; height:34px; font-size:20px; line-height:34px; text-indent:5px; }