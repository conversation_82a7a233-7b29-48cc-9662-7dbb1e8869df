 .add-home {
    position: absolute;
    bottom: 10px;
    left: 0;
    right: 0;
    z-index: 10
}
.add-home-content {
    border: solid 1px #000;
    border-radius: 17px;
    height: 124px;
    padding-left: 70px;
    padding-top: 5px;
    background: rgba(255, 255, 255, .9);
    max-width: 400px;
    position: relative;
    margin: 0 auto
}
.add-home img {
    height: 100px;
    position: absolute;
    top: 50%;
    margin-top: -50px;
    left: 30px
}
.add-home p {
    margin: 13px 81px 0px;
    width: 232px;
}
.add-home em {
    position: absolute;
    bottom: -20px;
    left: 50%;
    margin-left: -10px;
    width: 0;
    height: 0;
    border: 10px solid #000;
    border-color: #000 transparent transparent;
    z-index: 5
}
.add-home em:before {
    content: "";
    position: absolute;
    bottom: -8px;
    right: -9px;
    width: 0;
    height: 0;
    border: 9px solid rgba(255, 254, 241, .9);
    border-color: #fffef1 transparent transparent;
    z-index: 3
}
.add-home .ion-ios-upload-outline {
    font-size: 22px;
    margin-left: 207px;
    color: #007aff;
    height: 40px;
    width: 40px;
}
.add-home .ion-close-round {
    position: absolute;
    display: block;
	top: 54px;
    left: 359px;
    width: 28px;
    height: 31px;
    line-height: 25px;
    text-align: center;
    color: rgba(0, 0, 0, .7)
}