<?php /* Smarty version 2.6.18, created on 2025-01-13 16:38:04
         compiled from header2.html */ ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" <?php if ($this->_tpl_vars['rkey'] == 0): ?>oncontextmenu="return false"<?php endif; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title><?php echo $this->_tpl_vars['title']; ?>
</title>
<link href="/css/default/jquery-ui.css" rel="stylesheet" type="text/css" />
<link href="/css/default/master.css" rel="stylesheet" type="text/css" />
<link href="/css/default/layout.css" rel="stylesheet" type="text/css" />
<link href="/css/default/info.css" rel="stylesheet" type="text/css" />
<script language="javascript" src="/js/jquery-1.8.3.min.js"></script>
<script type="text/javascript" src="../js/jquery-ui.js"></script>

<script language="javascript">
function changeh(){
    var obj = parent.document.getElementById("frame"); //取得父页面IFrame对象  
	var h= document.body.clientHeight+500;
    obj.style.height = h+"px"; //调整父页面中IFrame的高度为此页面的高度
}
var globalpath = '<?php echo $this->_tpl_vars['globalpath']; ?>
';
</script>