@CHARSET "UTF-8";

html {
	*overflow: hidden;
}

body {
	color: #000000;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-size: 12px;
	margin: 0;
	padding: 0;
}

ul li {
	list-style: none outside none;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}

table td,table th {
	border: 1px solid;
	padding: 0;
}

/*layout*/
#header,#main,#side,.frame,#footer {
	overflow: hidden;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100%;
}

#main {
	top: 135px;
	bottom: 27px;
	background: #b44249 url("/css/default/img/red/side_bg.jpg") no-repeat left bottom;
	min-width: 1225px;
}

#side {
	right: auto;
	width: 189px;
}

.frame {
	background: url("/css/default/img/red/main_con_bg.png") repeat 0 0 #000;
	left: 190px;
	width: auto;
}

#header {
	bottom: auto;
	height: 135px;
	min-width: 1225px;
	background: url("/css/default/img/red/nav_bg.png") repeat-x 0 0;
}

a,a:hover {
	text-decoration: none;
}

.logo {
	left: 7px;
	position: absolute;
	top: 5px;
	width: 135px;
}

.logo span {
	color: #fff;
	font-size: 30px;
	font-weight: bold;
	line-height: 50px;
	margin-left: 30px;
}

.top {
	overflow: hidden;
}

.header .menu {
	color: #FDE6EB;
	height: 53px;
	padding-right: 0;
	padding-top: 0;
	width: 100%;
}

.menu a {
	color: #FDE6EB;
	text-align: center;
}

.header .menu a {
	display: inline-block;
	text-align: center;
}

.header .menu2 a {
	width: 5em;
}

.header .menu a:hover,.header .menu .selected {
	color: #FFCC00;
	font-weight: bold;
}

.header .menu1 {
	background: url("/css/default/img/red/top_bg.png") no-repeat 0 0;
	float: left;
	height: 52px;
	margin-left: 200px;
	width: 538px;
}

.header .menu2 {
	float: left;
	margin: 5px 14px 0;
	line-height: 22px;
}

.header .menu3 a{
    float: left;
    margin: 23px 8px 0;
    line-height: 22px;
    background-image: url("/css/default/img/red/logout.png");
	width: 70px;
	height: 25px;
}

.header .menu3 a:hover{
    background-image: url("/css/default/img/red/logout_rollover.png");
}

.header .menu4 a {
    float: left;
    margin: 23px 8px 0;
    line-height: 22px;
    background-image: url("/css/default/img/support.png");
    width: 70px;
    height: 25px;
}

.field-c {
    text-align: center;
    margin-top: 15px;
}

#skinPanel {
	display: inline-block;
}

#skinPanel ul {
	padding: 0 0 10px 0;
	margin: 0;
	list-style: none;
	position: absolute;
	width: 5em;
	z-index: 999;
}

#skinPanel ul li {
	padding: 0 6px;
	height: 20px;
	line-height: 20px;
	clear: both;
}

#skinPanel i {
	float: left;
	display: inline-block;
	border: 1px solid #ffc8c8;
	height: 9px;
	width: 9px;
	margin-top: 7px;
}

#skinPanel a {
	width: 5em;
}

#skinPanel ul a {
	width: 3em;
}

/*.header .menu .logout {
	color: #FFDE00;
	width: 3em;
}*/

.header .lotterys {
	height: 46px;
	line-height: 57px;
	margin-left: 2px;
	margin-top: 0;
	padding-left: 0;
	position: relative;
}

.header .lotterys .show {
	float: left;
}

.header .lotterys a {
	color: #F9F9F9;
	display: inline-block;
	float: left;
	font-weight: bold;
	height: 46px;
	margin: 0;
	text-align: center;
}

.header .lotterys a:hover {
	color: #ff0;
}

.header .lotterys a span {
	display: block;
	width: 117px;
	height: 46px;
	background: url(/css/default/img/red/nav_shuxian.png) no-repeat right 22px;
}

.more_game {
	color: #ff0;
}

.header .lotterys .selected {
	background: url("/css/default/img/red/nav_hover.png") no-repeat 4px 0;
	color: #FFFF00;
}

.header .lotterys .setting {
	background: url("/css/default/img/setting.png") no-repeat 10px 18px;
	padding-left: 35px;
	cursor: pointer;
	color: rgb(252, 247, 247);
	position: absolute;
	left: 1054px;
}

.header .lotterys .home {
    background: url("/css/default/img/home.png") no-repeat 10px 18px;
    padding-left: 35px;
    cursor: pointer;
    color: rgb(252, 247, 247);
    margin-left: 15px;
    position: absolute;
	left: 1108px;
}

#lotteryChoose p {
	color: red;
}

.popPanel {
	width: 108px;
	padding-bottom: 10px;
	background-color: #490418;
	position: absolute;
	z-index: 2000;
	left: 942px;
	top: 99px;
	display: none;
}

.popPanel a {
	display: block;
	width: 106px;
	height: 30px;
	color: #fff;
	text-align: center;
	line-height: 30px;
}

.popPanel a:hover {
	background-color: #823948;
	color: #ff0;
}

.header .sub {
	color: #fff;
	height: 36px;
	line-height: 36px;
	padding-left: 186px;
}

.header .sub a {
	color: #fff;
	padding: 0 0.5em;
}

.header .sub a:hover,.header .sub .selected {
	color: #FFFF00;
	font-weight: bold;
}

.side_left {
	
}

.side_left ul {
	list-style: none outside none;
	margin: 0;
	padding: 0;
}

.side_left .user_info {
	overflow: hidden;
}

.side_left .info {
	clear: both;
	overflow: hidden;
}

.side_left .info span,.side_left .info label {
	display: block;
	float: left;
	height: 22px;
	line-height: 24px;
	text-indent: 10px;
}

.side_left .info label {
	color: #FEADB5;
	text-align: right;
}

.side_left .info span {
	color: #FFFFFF;
}

.zhanghu {
	margin: 6px 0 8px;
}

.side_left #balance {
	font-weight: bold;
}

.side_left .title {
	background: url("/css/default/img/red/left_bg.png") no-repeat 0 0;
	color: #fff;
	font-weight: bold;
	height: 33px;
	line-height: 33px;
	text-align: center;
	width: 189px;
	margin-top: 4px;
    margin-left: 6px;
}

.side_left .worklogo {
	height: 40px;
	width: 177px;
	position: absolute;
	bottom: 2px;
	overflow: hidden;
}

.worklogo img {
	border: 0;
	width: 177px;
}

.side_left #changeVer {
	color: #162E92;
	display: block;
	height: 35px;
	line-height: 35px;
}

.left_a li a {
	color: #FFFFFF;
}

#drawOfficial {
	clear: both;
	overflow: hidden;
	width: 189px;
}

#drawOfficial ul {
	margin: 8px 0 10px;
	overflow: hidden;
}

#drawOfficial li {
	color: #FFFFFF;
	float: left;
	line-height: 24px;
	margin: 0 10px;
	width: 157px;
}

#drawOfficial a {
	color: #fff;
}

#drawOfficial a:hover {
	color: red;
}

.side_left .control {
	background: white;
	text-align: center;
	height: 25px;
	border: 1px solid;
}

.side_left .control a {
	background: url("/css/default/img/red/btn-bg.png") repeat-x;
	width: 66px;
	height: 18px;
	line-height: 18px;
	color: #fff;
	display: inline-block;
	margin-top: 4px;
}

.skin_blue .side_left .control a {
	background: url("/css/default/img/blue/btn-bg.png") repeat-x;
}

.side_left p {
	margin: 0;
	/*height: 18px;*/
	line-height: 18px;
}

.side_left .bets .numbers span {
	color: #067ece;
}

.side_left .bets .contents {
	padding-left: 4em;
	text-indent: 0;
}

.side_left .bets .bid {
	color: #119400;
}

.side_left .bets .text {
	color: #0017C7;
	white-space: pre-wrap;
	word-wrap: break-word;
}

.side_left .bets .odds {
	font-family: Arial, Helvetica, Verdana, Geneva, sans-serif;
	color: red;
	font-weight: bold;
}

#betResultPanel {
	color: black;
}

#betResultPanel .bresults {
	max-height: 367px;
	overflow: auto;
}

#betResultPanel .bets {
	text-indent: 5px;
	border-left: 1px solid;
	border-right: 1px solid;
	border-top: 1px solid;
}

#betResultPanel li {
	padding: 1px;
	background: white;
	border-top: 1px solid;
}

#betResultPanel .Paneltitle {
	height: 35px;
	line-height: 35px;
	border: 1px solid;
	font-weight: bold;
	text-align: center;
	font-size: 14px;
}

#betResultPanel .total tr {
	text-align: center;
	height: 24px;
	text-indent: 10px;
	background: white;
}

#betResultPanel .total .label {
	width: 75px;
	background: #F8F9FE;
}

#betResultTotal {
	font-weight: bold;
}

#betResultPanel table {
	width: 100%;
}

#betResultPanel .bets td {
	background: white;
	text-align: center;
}

#betResultPanel .bets .head {
	background: #ebd7d7;
}

#betResultPanel .bets .id {
	width: 18%;
}

#betResultPanel .bets .nums {
	width: 54%;
}

#betResultPanel .bets .amount {
	text-align: left;
	width: 28%;
	text-indent: 2px;
}

#lastBets {
	margin-top: 10px;
}

#lastBets .title a {
	background: url("/css/default/img/blue/btn.png") no-repeat 0 0;
	position: relative;
	display: block;
	width: 37px;
	height: 18px;
	line-height: 18px;
	color: #fff;
	font-weight: normal;
	margin: -24px 0 0 180px;
}

#lastBets .title a:hover {
	background-position: -38px 0;
}

#lastBets .bets {
	text-indent: 5px;
	border-left: 1px solid;
	border-right: 1px solid;
	color: black;
	max-height: 300px;
	overflow-y: auto;
	border-left: 1px solid #E6B3BE;
	border-right: 1px solid #E6B3BE;
}

.skin_blue #lastBets .bets {
	text-indent: 5px;
	border-left: 1px solid #b9c2cb;
	border-right: 1px solid #b9c2cb;
	color: black;
	max-height: 300px;
	overflow-y: auto;
}

#lastBets li {
	padding: 1px;
	background: white;
	border-top: 1px solid #E6B3BE;
}

#lastBets li:nth-child(even) {
	background: #efefef;
}

.skin_blue #lastBets li {
	padding: 1px;
	background: white;
	border-top: 1px solid #b9c2cb;
}

#lastBets .bets p {
	white-space: pre-wrap;
	word-wrap: break-word;
}

#frame {
	width: 100%;
	height: 100%;
}

#footer {
	top: auto;
	min-width: 1225px;
	background: url("/css/default/img/marquebg.png") repeat-x;
  	border-bottom: 1px solid #E6B3BE;
  	border-top: 1px solid #E6B3BE;
  	line-height: 27px;
  	height: 27px;
  	text-indent: 3px;
}

#footer .info {
	padding-right: 65px;
}

#footer .more {
	position: absolute;
	right: 18px;
	top: 0;
	color: #762D08;
}

#footer span {
    color: #35406D;
}

.skin_blue #footer span {
    color: #35406D;
}

#footer span {
    color: #626262;
}

.ui-widget-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: #888;
	opacity: 0.3;
}

.ui-front {
	z-index: 100;
}

.ui-dialog {
	border: 5px solid #DF678D;
	background: white;
}

.ui-dialog-titlebar {
	padding: 4px 0;
	line-height: 16px;
}

.ui-dialog-icon {
	display: inline-block;
	background-position: -52px -52px;
	height: 11px;
	width: 14px;
	margin: 0 4px 0 10px;
}

.ui-dialog-title {
	font-size: 13px;
	font-weight: 700;
	color: #B30B00;
	margin-left: 7px;
}

.ui-dialog-titlebar-close {
	background-position: -52px -42px;
	height: 9px;
	width: 11px;
	border: none;
	margin: 3px 4px 0 0;
	padding: 0;
	float: right;
	text-indent: 99999px;
	font-size: 0;
}

.ui-dialog-buttonset {
	border: 1px solid white;
	text-align: center;
	padding: 8px 0;
}

.ui-dialog-buttonset .ui-button {
	border: none;
	width: 66px;
	height: 18px;
	line-height: 18px;
	margin-right: 5px;
	color: #fff;
	letter-spacing: 3px;
}

.ui-dialog-buttonset .ui-state-hover {
	background-position: 0 -19px;
	color: #FFE400;
	font-weight: bold;
}

.table {
	word-wrap: break-word;
	width: 100%;
}

.table thead th {
	text-align: center;
	color: #762d08;
	height: 24px;
	line-height: 24px;
	background: #E57388 url("/css/default/img/red/table_headerbg.gif") repeat-x left top;
}

.skin_blue .table thead th {
	background: #c5e5f4 url("/css/default/img/blue/table_headerbg.gif") repeat-x left top;
	color: #35406d;
}

.skin_gray .table thead th {
	background: #c5e5f4 url("/css/default/img/gray/table_headerbg.png") repeat-x left top;
	color: #fff;
}

.notices tbody td {
	font-weight: normal;
	text-align: center;
	color: #302F2F;
	font-size: 13px;
}

.notices .contents {
	text-align: left;
	text-indent: 2em;
}

#betsBox table {
	text-align: center;
}

#betsBox table th {
	height: 24px;
	white-space: nowrap;
}

#betsBox table td {
	height: 26px;
}

#betsBox .contents {
	text-align: left;
	text-indent: 1em;
}

#betsBox .odds {
	color: red;
}

#betsBox .amount input {
	width: 50px;
}

#betsBox .check {
	width: 28px;
}

#betsBox a {
	color: blue;
	cursor: pointer;
}

#betsBox .multiple ol {
	position: absolute;
	background: white;
	width: 300px;
	padding: 5px;
	margin: 0;
	border: 1px solid;
	list-style-position: inside;
}

#betsBox .multiple li {
	float: left;
	margin: 0 5px;
}

#betsBox .multiple li span {
	color: blue;
}

#betsBox .bottom {
	border: 1px solid;
}

#betsBox .bottom span {
	display: inline-block;
	height: 26px;
	text-align: center;
	line-height: 26px;
}

#bcount {
	width: 127px;
	border-right: 1px solid;
}

#btotal {
	width: 254px;
}

#betsBox .betList {
	max-height: 300px;
	overflow-x: hidden;
	overflow-y: auto;
}

.skin_red {
	background: url("/css/default/img/red/main_bg.jpg") no-repeat 0 0 #A12F37;
}

.skin_red .ui-dialog {
	border-color: #DF678D;
}

.skin_red .ui-dialog-titlebar {
	background: url("/css/default/img/red/openwinbg.png") repeat-x top;
}

.skin_red .ui-dialog-icon,.skin_red .ui-dialog-titlebar-close {
	background-image: url("/css/default/img/red/btn.png");
}

.skin_red .ui-dialog-content {
	border-bottom: 1px solid #E6B3BE;
	padding: 8px 8px 16px 8px;
}

.skin_red .ui-dialog-buttonset {
	background: #FFEAF2;
}

.skin_red .ui-dialog-buttonset .ui-button {
	background-image: url(/css/default/img/red/btn-bg.png);
}

.skin_red #betResultPanel .bets,.skin_red #betResultPanel li,.skin_red .control,.skin_red table td,.skin_red table th,.skin_red #betsBox .bottom,.skin_red #bcount
	{
	border-color: #E6B3BE;
}

.skin_red .skinHover,.skin_red .skinHover ul {
	background: #B0565A;
}

.menu1 .draw_number {
	height: 50px;
	display: inline-block;
	float: left;
	margin-left: 48px;
}

.menu1 .draw_number div {
	height: 22px;
	line-height: 22px;
	text-align: center;
}

.menu1 a {
	display: block;
	float: left;
	margin-left: 10px;
}

.info .input {
	width: 60px;
}

.contents .text {
	color: #2836f4;
}

#betBox .drawNumber {
	color: #299a26;
}

.contents .odds {
	color: red;
	font-weight: bold;
	font-family: Verdana, Arial, Sans;
}

.plus {
	height: 24px;
	line-height: 24px;
}

/*======================================== skin_blue =========================================*/
.skin_blue body {
	color: #495181;
}

.skin_blue #main {
	background: #2f6ed7 url("/css/default/img/blue/side_bg.jpg") no-repeat left top;
}

.skin_blue #side {
	margin: 0 0 0 0;
	right: auto;
	width: 189px;
}

.skin_blue .frame {
	background: url("/css/default/img/blue/main_con_bg.png") repeat 0 0 #000;
}

.skin_blue #header {
	background-image: url("/css/default/img/blue/nav_bg.png");
}

.skin_blue .header .menu {
	color: #EBEFF5;
}

.skin_blue .header .menu a {
	color: #EBEFF5;
	display: inline-block;
	text-align: center;
}

.skin_blue .header .menu2 a {
	width: 5em;
}

.skin_blue .header .menu a:hover,.header .menu .selected {
	color: #ffff00;
}

.skin_blue .header .menu1 {
	background: url("/css/default/img/blue/top_bg.png");
}

.skin_blue .header .lotterys a {
	color: #fff;
}

.skin_blue .header .lotterys a:hover {
	color: #FFFF00;
}

.skin_blue .header .lotterys a span {
	width: 117px;
	background: url(/css/default/img/blue/nav_shuxian.png) no-repeat right 22px;
}

.skin_blue .more_game {
	color: #4b5465;
}

.skin_blue .header .lotterys .selected {
	background: url(/css/default/img/blue/nav_hover.png) no-repeat 4px 0;
	color: #FFFF00;
}

.skin_blue .header .sub {
	color: #FFF;
	height: 36px;
	line-height: 36px;
	padding-left: 186px;
}

.skin_blue .header .sub a {
	padding: 0 0.5em;
}

.skin_blue .header .sub a:hover,.skin_blue .header .sub .selected {
	color: #FFFF00;
	font-weight: bold;
}

.skin_blue .side_left .info label {
	color: #36639D;
}

.skin_blue .side_left .info span {
	color: #fff;
	width: 100px;
}

.skin_blue .side_left .title {
	background: url("/css/default/img/blue/left_bg.png") no-repeat 0 0;
	font-size: 13px;
	color: #fff;
	font-weight: bold;
	height: 33px;
	line-height: 33px;
	text-align: center;
	width: 183px;
	margin-top: 4px;
	margin-left: 6px;
}

.skin_blue .side_left .worklogo {
	width: 189px;
}

.skin_blue #drawOfficial {
	clear: both;
	overflow: hidden;
	width: 189px;
}

.skin_blue .popPanel a:hover {
	background-color: #264167;
}

.skin_blue #drawOfficial li {
	color: #959595;
}

.skin_blue #drawOfficial a {
	color: #fff;
}

.skin_blue #drawOfficial a:hover {
	color: red;
}

.skin_blue #footer {
  background: url("/css/default/img/marquebg.png") repeat-x;
  border-bottom: 1px solid #B5C6E2;
  border-top: 1px solid #B5C6E2;
  line-height: 27px;
  height: 27px;
  text-indent: 3px;
}

.skin_blue #footer .more {
	color: #3e34fc;
}

.skin_blue .ui-dialog-title {
	color: #35406d;
}

.skin_blue .field-c {
    text-align: center;
    margin-top: 20px;
}

.skin_red .field-c {
    text-align: center;
    margin-top: 20px;
}

.skin_gray .field-c {
    text-align: center;
    margin-top: 20px;
}

.skin_blue {
	background: url("/css/default/img/blue/main_bg.jpg") no-repeat 0 0 #3682D0;
}

.skin_blue .ui-dialog {
	border-color: #6C8AC2;
}

.skin_blue .ui-dialog-content {
	border-bottom: 1px solid #b9c2cb;
}

.skin_blue .ui-dialog-buttonset {
	background: #edf4fe;
}

.skin_blue .skinHover,.skin_blue .skinHover ul {
	background: #234b95;
}

.skin_blue .ui-dialog-titlebar {
	background: url("/css/default/img/blue/openwinbg.png") repeat-x top;
}

.skin_blue .ui-dialog-icon,.skin_blue .ui-dialog-titlebar-close {
	background-image: url("/css/default/img/blue/btn.png");
}

.skin_blue .ui-dialog-content {
	padding: 8px 8px 16px 8px;
}

.skin_blue .ui-dialog-buttonset .ui-button {
	background-image: url("/css/default/img/blue/btn-bg.png");
}

.skin_blue #betResultPanel .Paneltitle {
	background: url("/css/default/img/blue/th_bg.png") repeat-x 0 -100px;
	color: #F5F6F8;
	border-color: #88ABDA;
	background-color: #88ABDA;
}

.skin_blue #betResultPanel .bets,.skin_blue #betResultPanel li,.skin_blue .control,.skin_blue table td,.skin_blue table th,.skin_blue #betsBox .bottom,.skin_blue #bcount
	{
	border-color: #b9c2cb;
}

.skin_blue .header .menu3 a{
    background-image: url("/css/default/img/blue/logout.png");
}

.skin_blue .header .menu3 a:hover{
    background-image: url("/css/default/img/blue/logout_rollover.png");
}

.skin_blue .popPanel {
	background-color: #386BAD;
}

/*======================================== skin_gray =========================================*/
.skin_gray body {
	color: #495181;
}

.skin_gray #main {
	background: #c0c0c0 url("/css/default/img/gray/side_bg.jpg") no-repeat left top;
}

.skin_gray #side {
	margin: 0 0 0 0;
	right: auto;
	width: 189px;
}

.skin_gray .popPanel {
	background-color: #BDBDBD;
}

.skin_gray .frame {
	background: url("/css/default/img/gray/main_con_bg.png") repeat 0 0 #000;
}

.skin_gray #header {
	background-image: url("/css/default/img/gray/nav_bg.png");
}

.skin_gray .header .menu {
	color: #000000;
}

.skin_gray .header .menu a {
	color: #000000;
	display: inline-block;
	text-align: center;
}

.skin_gray .header .menu2 a {
	width: 5em;
}

.skin_gray .header .menu a:hover,.header .menu .selected {
	color: #000000;
}

.skin_gray .header .menu1 {
	background: url("/css/default/img/gray/top_bg.png");
}

.skin_gray .header .lotterys a {
	color: #fff;
}

.skin_gray .header .lotterys a:hover {
	color: #FFFF00;
}

.skin_gray .header .lotterys a span {
	width: 117px;
	background: url(/css/default/img/gray/nav_shuxian.png) no-repeat right 22px;
}

.skin_gray .more_game {
	color: #4b5465;
}

.skin_gray .header .lotterys .selected {
	background: url(/css/default/img/gray/nav_hover.png) no-repeat 4px 0;
	color: #FFFF00;
}

.skin_gray .header .sub {
	color: #FFF;
	height: 36px;
	line-height: 36px;
	padding-left: 186px;
}

.skin_gray .header .sub a {
	padding: 0 0.5em;
}

.skin_gray .header .sub a:hover,.skin_gray .header .sub .selected {
	color: #FFFF00;
	font-weight: bold;
}

.skin_gray .side_left .info label {
	color: #000000;
}

.skin_gray .popPanel a:hover {
	background-color: #cccccc;
	color: #000000;
}

.skin_gray .side_left .info span {
	color: #D8A328;
	width: 90px;
}

.skin_gray .side_left .title {
	background: url("/css/default/img/gray/left_bg.png") no-repeat 0 0;
	font-size: 13px;
	color: #fff;
	font-weight: bold;
	height: 33px;
	line-height: 33px;
	text-align: center;
	width: 183px;
	margin-top: 4px;
	margin-left: 6px;
}

.skin_gray .side_left .worklogo {
	width: 189px;
}

.skin_gray #drawOfficial {
	clear: both;
	overflow: hidden;
	width: 189px;
}

.skin_gray #drawOfficial li {
	color: #959595;
}

.skin_gray #drawOfficial a {
	color: #fff;
}

.skin_gray #drawOfficial a:hover {
	color: red;
}

.skin_gray #footer {
  background: url("/css/default/img/marquebg.png") repeat-x;
  border-bottom: 1px solid #9B9BA2;
  border-top: 1px solid #9B9BA2;
  line-height: 27px;
  height: 27px;
  text-indent: 3px;
}

.skin_gray #footer .more {
	color: #313133;
}

.skin_gray .ui-dialog-title {
	color: #313133;
}

.skin_gray {
	background: #C6C6C6;
}

.skin_gray .ui-dialog {
	border-color: #AAAAAA;
}

.skin_gray .ui-dialog-content {
	border-bottom: 1px solid #cccccc;
}

.skin_gray .ui-dialog-buttonset {
	background: #cccccc;
}

.skin_gray .skinHover,.skin_gray .skinHover ul {
	background: #B2B2B2;
}

.skin_gray .ui-dialog-titlebar {
	background: url("/css/default/img/gray/openwinbg.png") repeat-x top;
}

.skin_gray .ui-dialog-icon,.skin_gray .ui-dialog-titlebar-close {
	background-image: url("/css/default/img/gray/btn.png");
}

.skin_gray .ui-dialog-content {
	padding: 8px 8px 16px 8px;
}

.skin_gray .ui-dialog-buttonset .ui-button {
	background-image: url("/css/default/img/gray/btn-bg.png");
}

.skin_gray #betResultPanel .Paneltitle {
	background: url("/css/default/img/gray/th_bg.png") repeat-x 0 -100px;
	color: #fff;
    border-color: #818181;
    background-color: #818181;
}

.skin_gray #betResultPanel .bets,.skin_gray #betResultPanel li,.skin_gray .control,.skin_gray table td,.skin_gray table th,.skin_gray #betsBox .bottom,.skin_gray #bcount
	{
	border-color: #b9c2cb;
}

.skin_gray .header .menu3 a{
    background-image: url("/css/default/img/gray/logout.png");
}

.skin_gray .header .menu3 a:hover{
    background-image: url("/css/default/img/gray/logout_rollover.png");
}

.skin_gray #lastBets li {
    border-top: 1px solid #CCCCCC;
}

.skin_gray #lastBets .bets {
    border-left: 1px solid #CCCCCC;
    border-right: 1px solid #CCCCCC;
}


#share_dialog {
    background-color: #fff;
    height: 265px;
    left: 38%;
    margin: -100px 0 0 -70px;
    padding: 1px;
    position: fixed !important;
    position: absolute;
    top: 40%;
    width: 480px;
    z-index: 1002;
    background: url("/css/default/img/bjl_announcement_2a.jpg") no-repeat -0px -0px;
}

#share_dialog a {
    color: #fff;
    text-decoration: none;
    display: block;
    width: 50px;
    height: 50px;
    float: right;
}

.loading_overlay {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 1001;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    display: table;
    opacity: 0.8;
    background: #666;
}
