.content {
	position: relative;
}

.memberheader {
	height: 140px;
	border-bottom: 1px solid #cdcdcd;
	position: relative;
}

.useravatar {
	width: 84px;
	height: 83px;
	margin: 25px 0 0 25px;

}
.memberinfo {
	margin: 22px 0 0 15px;
	width: 475px;
}
.memberinfo h1 {
	font-weight: normal;
	margin: 0;
}

.memberinfo p {
	font-size: 12px;
}

.balance {
		/*width: 183px;*/
		height: 32px;
		/*background-image:url(./img/panelcash.jpg);*/
		margin-top: 5px;
		margin-left: 20px;
}

.balancevalue {
	margin: 6px 0 0 40px;	
}


.membersubnavi {
	position: absolute;
	top: 95px;
	right: 0;
	/*width: 425px;*/
}

.subnavi {
	padding: 0 20px;
	float: left;
	font-size: 17px;
}

.subnavi a {
	color: #777f89;	
}
.subnaviarrow {
	margin-top: 5px;
	
	background-image: url(./img/subnaviarrow.jpg);
	background-position: top center;
	height: 20px;
	background-repeat:no-repeat;
}

.steps {
	height: 130px;
	position: relative;
}

.substeps {
	margin-top: 30px;
	margin-left: 30px;
}

.substepitmgray1 {
	width: 183px;
	height: 25px;
	float:left;
	background-image:url(./img/arrowgray1.png);
	color: #fff;
	padding: 5px 0 0 55px;
}

.substepitmblue1 {
	background-image:url(./img/arrowblue1.png);
}


.substepitmgray2 {
	width: 163px;
	height: 25px;
	float:left;
	margin-left: -4px;
	background-image:url(./img/arrowblue2.png);
	color: #fff;
	padding: 5px 0 0 75px;
}

.substepitmblue2 {
	background-image:url(./img/arrowblue2.png);
}


.line {
	height: 1px;
	position: absolute;
	bottom: 0;
	background-color: #217eec;	
	width: 100%;
}

.tabs {
	position: absolute;
	bottom: 0;
	left: 0;
}

.tabtitle {
	width: 120px;
	height: 30px;
	padding-top: 6px;
	text-align:center;
	float:left;
}

.tab {
	float:left;
	width: 170px;
	height: 28px;
	padding-top: 8px;
	margin-left: 10px;
	border-top: 1px solid #b0b1b1;
	border-left: 1px solid #b0b1b1;
	border-right: 1px solid #b0b1b1;
	border-bottom: 1px solid #217eec;
	text-align:center;
	background-color: #e3e6e8;
	color: #777f89;
	cursor: pointer;
}

.tabactive {
	color: #217eec;
	border-top: 1px solid #217eec;
	border-left: 1px solid #217eec;
	border-right: 1px solid #217eec;
	border-bottom: 1px solid #fff;	
	background-color: #fff;
	cursor: default;
}

.subcontent {
	display: none;
	/*border-left: 1px solid #217eec;
    border-right: 1px solid #217eec;
    border-bottom: 1px solid #217eec;*/
}

.subrow {
	margin: 15px 0;
}

.subrow .subcol1 {
	width: 110px;
	float: left;
	padding-top: 4px;	
	text-align: right;
}

.subrow .subcol2 {
	margin-left: 10px;
	width: 890px;
	float: left;	
}

.subrow .subcol3 {
    margin-left: 120px;
    width: 890px;
    float: left;
}

.textfield1 {
	padding: 0 5px;
    width: 160px;
    height: 26px;
    line-height: 26px;
    border: 1px solid #ccc;
    border-width: 1px;
    -webkit-border-radius: 2px;
    -webkit-box-shadow: 0 1px 2px 0 #E8E8E8 inset;
    -moz-border-radius: 2px;
    -moz-box-shadow: 0 1px 2px 0 #E8E8E8 inset;
    -o-border-radius: 2px;
    -o-box-shadow: 0 1px 2px 0 #E8E8E8 inset;
    -ms-border-radius: 2px;
    -ms-box-shadow: 0 1px 2px 0 #E8E8E8 inset;
    border-radius: 2px;
    box-shadow: 0 1px 2px 0 #E8E8E8 inset;
}

.text{
	width: 260px;
    height: 18px;
    border: 1px solid #bdc6ca;
    background: #ffffff;
    border-radius: 2px;
    font-size: 14px;
    line-height: 18px;
    padding: 9px 5px;
}

.banklist {
	width: 117px;
	height: 35px;
	float:left;
	border: 1px solid #CEC9C9;
	margin-right: 10px;
	cursor: pointer;
	background-color: #fff;
}

.banklist:hover {
	border: 1px solid #DBE8EC;
}

.banklistimg {
	float: left;
	margin: 8px 0 0 5px;
}


.bankradiobtn {
	float:left;
	margin: 12px 0 0 7px;
}



.submitbtn {
	border: 1px solid #a2a2a2;
	background-color: #fff;
	color: #a2a2a2;
	padding: 7px 20px;
	cursor:pointer;
}

.submitbtn:hover {
	border: 1px solid #a2a2a2;
	background-color: #a2a2a2;
	color: #fff;
}

.banklist2 {
	width: 174px;
	height: 40px;
	float:left;
	border: 1px solid #CEC9C9;
	margin-right: 10px;
	cursor: pointer;
	position: relative;
	background-color: #fff;
}

.banklistimg2 {
	float: left;
	margin: 5px 0 0 5px;
}

.banklist2:hover {
	border: 1px solid #DBE8EC;
}

.bankradiobtn2 {
	float:left;
	margin: 12px 0 0 7px;
}

.bankflag {
	position: absolute;
	top: 0px;
	right: 0px;
	padding: 2px;
	background-color:#cccccc;
	font-size: 12px;
}

.subrow .button {
    display: block;
    width: 152px;
    height: 36px;
    font-size: 14px;
    line-height: 35px;
    text-align: center;
}



.transaction-setting-container table{
	width: 100%;
	color: #787878;
	padding: 10px 0 20px;	
	/*border: 1px solid #e5e5e5;*/
}

.transaction-setting-container table input{
	height: 25px;
	float: left;
	margin-top: 3px;
	margin-right: 5px;
	margin-left: 5px;
	color: #222;
	text-align: center;
	width: 122px;
}

.transaction-setting-container table div.date{
	margin-right: 10px;
}

.transaction-setting-container table div.date img{
	margin-top: 3px;
	margin-left: 6px;
}

.transaction-setting-container table a{
	color: #787878;
}

.setting-table tr td{
	padding: 5px;
}

.setting-table tr td:first-child{
	width: 115px;
	text-align: right;
}

.setting-table tbody tr td div{
	width: 170px;
	height: 33px;
	border: 1px solid #d5d5d5;
}

.setting-table tbody tr td div.date{
	float: left;
}

.setting-table tbody tr td div.search{
	border: 1px solid #d5d5d5;
	background-color: #fff;
	width: 180px;
}

.setting-table tbody tr td div.search input{
	float: left;
	margin-top: 3px;
	color: #222;
	background-color: #fff;
	width: 122px;
}

.setting-table tbody tr td div.search .btn{
	margin-top: 3px;
}

.setting-table tbody tr td ul li{
	padding: 5px 15px;
	margin-right: 5px;
	float: left;
}

.setting-table tbody tr td ul li.active{
	background-color: #0b8fff;
}

.setting-table tbody tr td ul li.active a{
	color: #fff;
}

.alignRight{
	text-align: right;
}

.duration-setting-container{
	width: 100%;
	padding: 5px 50px;
}

.duration-setting-container ul li{
	padding: 5px 15px;
	float: left;
	margin-right: 10px;
}

.duration-setting-container ul li.active{
	background-color: #0b8fff;
}

.duration-setting-container ul li.active a{
	color: #fff;
}

.records{
	padding: 0px 5px;
}

.records table{
	width: 100%;
	border-collapse: collapse !important;
}

.records table th{
	padding: 10px 0;
	font-weight: lighter;
	border: 1px solid #777f89;
	background-color: #777f89;
	color:#fff;
}

.records table td{
	font-size: 12px;
	color: #777f89;
	text-align: center;
	padding: 10px 10px;
}

.records table td a{
	color: #0b8fff;
}

.records table td a:hover{
	text-decoration: underline;
}

.records table tr td:first-child, .records table tr th:first-child{
	width: 40px;
}

.banklist i {
    margin: 0 6px 0 8px;
    margin-top: 9px;
    vertical-align: -4px;
    display: inline-block;
}

.ico-icbc, .ico-cmb, .ico-ccb, .ico-abc, .ico-boc, .ico-spdb, .ico-sdb, .ico-cib, .ico-bob, .ico-cebb, .ico-bcom, .ico-cmbc, .ico-ecitic, .ico-gdb, .ico-pingan, .ico-post, .ico-union, .ico-jsb, .ico-srcb, .ico-hkb, .ico-nbcb, .ico-njcb, .ico-bosh, .ico-hxb, .ico-hzb, .ico-hkbea, .ico-ordos, .ico-cbhb, .ico-jzb, .ico-gdrcu, .ico-nccb, .ico-glccb, .ico-bsb, .ico-ynrcc, .ico-gzcb, .ico-cqrcb, .ico-zjcb ,.ico-psbc{
    margin: 0 6px 0 8px;
    background: url(./img/bank_ico.png) no-repeat;
    display: inline-block;
    width: 18px;
    height: 18px;
    margin-right: 3px;
    _margin-top: 4px;
    vertical-align: -4px;
}

.ico-zfb{
    background: url(./img/zfb.png) no-repeat;
    width: 18px;
    height: 19px;
    margin-right: 3px;
    _margin-top: 4px;
    vertical-align: -4px;
}

.ico-wxzf{
    background: url(./img/wxfb.png) no-repeat;
    width: 18px;
    height: 19px;
    margin-right: 3px;
    _margin-top: 4px;
    vertical-align: -4px;
}

.btn-captcha {
    padding: 0 10px;
    height: 27px;
    line-height: 27px;
    text-align: center;
    border: 1px solid #dddddd;
    background: #f4f4f4;
    cursor: pointer;
    color: #555;
}

.ico-icbc {
    background-position: 0 0;
}
.ico-cmb {
    background-position: -18px 0;
}
.ico-ccb {
    background-position: -36px 0;
}
.ico-abc {
    background-position: -54px 0;
}
.ico-psbc {
    background-position: -18px -18px;
}
.ico-pingan {
    background-position: 0 -36px;
}
.ico-sdb {
    background-position: -108px -18px;
}
.ico-gdb {
    background-position: -72px -18px;
}
.ico-bosh {
    background-position: 0 -54px;
}
.ico-bob {
    background-position: -126px -18px;
}
.ico-bcom {
    background-position: -36px -18px;
}
.ico-cmbc {
    background-position: -90px -18px;
}
.ico-cbhb {
    background-position: -108px -54px;
}
.ico-hkbea {
    background-position: -54px -54px;
}
.ico-nbcb {
    background-position: -90px -36px;
}
.ico-ecitic {
    background-position: -126px 0;
}
.ico-bjrcb {
    background-position: -32px 0;
}
.ico-hxb {
    background-position: -18px -54px;
}
.ico-cz {
    background-position: 0 0;
}
.ico-hzb {
    background-position: -36px -54px;
}
.ico-boc {
    background-position: -72px 0;
}
.ico-cebb {
    background-position: -90px 0;
}
.ico-cib {
    background-position: 0 -18px;
}

.trcolor{
	background-color: #F0F0F1;
}


.page_info {
	PADDING-RIGHT: 3px; PADDING-LEFT: 3px; PADDING-BOTTOM: 3px; MARGIN: 3px; PADDING-TOP: 3px; TEXT-ALIGN: center
}
.page_info A {
	BORDER-RIGHT: #eee 1px solid; PADDING-RIGHT: 5px; BORDER-TOP: #eee 1px solid; PADDING-LEFT: 5px; PADDING-BOTTOM: 2px; MARGIN: 2px; BORDER-LEFT: #eee 1px solid; COLOR: #036cb4; PADDING-TOP: 2px; BORDER-BOTTOM: #eee 1px solid; TEXT-DECORATION: none
}
.page_info A:hover {
	BORDER-RIGHT: #999 1px solid; BORDER-TOP: #999 1px solid; BORDER-LEFT: #999 1px solid; COLOR: #666; BORDER-BOTTOM: #999 1px solid
}
.page_info A:active {
	BORDER-RIGHT: #999 1px solid; BORDER-TOP: #999 1px solid; BORDER-LEFT: #999 1px solid; COLOR: #666; BORDER-BOTTOM: #999 1px solid
}
.page_info .current {
	BORDER-RIGHT: #036cb4 1px solid; PADDING-RIGHT: 5px; BORDER-TOP: #036cb4 1px solid; PADDING-LEFT: 5px; FONT-WEIGHT: bold; PADDING-BOTTOM: 2px; MARGIN: 2px; BORDER-LEFT: #036cb4 1px solid; COLOR: #fff; PADDING-TOP: 2px; BORDER-BOTTOM: #036cb4 1px solid; BACKGROUND-COLOR: #036cb4
}
.page_info .disabled {
	BORDER-RIGHT: #eee 1px solid; PADDING-RIGHT: 5px; BORDER-TOP: #eee 1px solid; PADDING-LEFT: 5px; PADDING-BOTTOM: 2px; MARGIN: 2px; BORDER-LEFT: #eee 1px solid; COLOR: #ddd; PADDING-TOP: 2px; BORDER-BOTTOM: #eee 1px solid
}